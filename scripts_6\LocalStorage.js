Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2StorageSync = require("StorageSync");
var def_LocalStorage = function () {
  function _ctor() {}
  _ctor.getItem = function (e) {
    if (undefined === window.localStorage) {
      return $2StorageSync.default.getItem(e);
    } else {
      return window.localStorage.getItem(e);
    }
  };
  _ctor.setItem = function (e, t) {
    if (undefined === window.localStorage) {
      return $2StorageSync.default.setItem(e, t);
    } else {
      return window.localStorage.setItem(e, t);
    }
  };
  _ctor.removeItem = function (e) {
    if (undefined === window.localStorage) {
      return $2StorageSync.default.removeItem(e);
    } else {
      return window.localStorage.removeItem(e);
    }
  };
  return _ctor;
}();
exports.default = def_LocalStorage;