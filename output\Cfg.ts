import { activityCfgReader } from "./activityCfg";
import { adRewardCfgReader } from "./adRewardCfg";
import { BagShopItemCfgReader } from "./BagShopItemCfg";
import { LvOutsideCfgReader } from "./LvOutsideCfg";
import { LvInsideCfgReader } from "./LvInsideCfg";
import { BulletEffectCfgReader } from "./BulletEffectCfg";
import { CurrencyConfigCfgReader } from "./CurrencyConfigCfg";
import { signCfgReader } from "./signCfg";
import { DropConfigCfgReader } from "./DropConfigCfg";
import { RoleLvCfgReader } from "./RoleLvCfg";
import { TowerLvCfgReader } from "./TowerLvCfg";
import { EquipLvCfgReader } from "./EquipLvCfg";
import { EquipMergeLvCfgReader } from "./EquipMergeLvCfg";
import { MiniGameEquipCfgReader } from "./MiniGameEquipCfg";
import { GameSettingCfgReader } from "./GameSettingCfg";
import { BagGuideCfgReader } from "./BagGuideCfg";
import { GuideCfgReader } from "./GuideCfg";
import { languageCfgReader } from "./languageCfg";
import { LevelExpCfgReader } from "./LevelExpCfg";
import { BoxLevelExpCfgReader } from "./BoxLevelExpCfg";
import { BagModeLvCfgReader } from "./BagModeLvCfg";
import { MiniGameLvCfgReader } from "./MiniGameLvCfg";
import { dragonPathCfgReader } from "./dragonPathCfg";
import { PayShopCfgReader } from "./PayShopCfg";
import { RoleUnlockCfgReader } from "./RoleUnlockCfg";
import { RoleCfgReader } from "./RoleCfg";
import { SoundCfgReader } from "./SoundCfg";
import { TaskCfgReader } from "./TaskCfg";
import { TaskTypeCfgReader } from "./TaskTypeCfg";
import { ProcessRewardsCfgReader } from "./ProcessRewardsCfg";
import { TowerCfgReader } from "./TowerCfg";
import { BagBuffCfgReader } from "./BagBuffCfg";
import { BuffCfgReader } from "./BuffCfg";
import { TowerCoinRewardCfgReader } from "./TowerCoinRewardCfg";
import { TowerAmethystRewardCfgReader } from "./TowerAmethystRewardCfg";
import { GameatrCfgReader } from "./GameatrCfg";
import { MapCfgReader } from "./MapCfg";
import { dmmRoleCfgReader } from "./dmmRoleCfg";
import { dmmItemCfgReader } from "./dmmItemCfg";
import { randomNameCfgReader } from "./randomNameCfg";
import { TowerMenuCfgReader } from "./TowerMenuCfg";
import { MonsterCfgReader } from "./MonsterCfg";
import { MonsterLvCfgReader } from "./MonsterLvCfg";
import { bagMonsterLvCfgReader } from "./bagMonsterLvCfg";
import { SkiilpoolCfgReader } from "./SkiilpoolCfg";
import { BuildModeSkiilpoolCfgReader } from "./BuildModeSkiilpoolCfg";
import { BagModeSkillPoolCfgReader } from "./BagModeSkillPoolCfg";
import { PoolListCfgReader } from "./PoolListCfg";
import { BagSkillCfgReader } from "./BagSkillCfg";
import { SkillCfgReader } from "./SkillCfg";
import { WeatherCfgReader } from "./WeatherCfg";
import { decompressFromBase64 } from "./lzstring";

class CfgManager {
    public static cfgLoadNum: number = 0;
    
    private _activity: activityCfgReader;
    private _adReward: adRewardCfgReader;
    private _BagShopItem: BagShopItemCfgReader;
    private _LvOutside: LvOutsideCfgReader;
    private _LvInside: LvInsideCfgReader;
    private _BulletEffect: BulletEffectCfgReader;
    private _CurrencyConfig: CurrencyConfigCfgReader;
    private _sign: signCfgReader;
    private _DropConfig: DropConfigCfgReader;
    private _RoleLv: RoleLvCfgReader;
    private _TowerLv: TowerLvCfgReader;
    private _EquipLv: EquipLvCfgReader;
    private _EquipMergeLv: EquipMergeLvCfgReader;
    private _MiniGameEquip: MiniGameEquipCfgReader;
    private _GameSetting: GameSettingCfgReader;
    private _BagGuide: BagGuideCfgReader;
    private _Guide: GuideCfgReader;
    private _language: languageCfgReader;
    private _LevelExp: LevelExpCfgReader;
    private _BoxLevelExp: BoxLevelExpCfgReader;
    private _BagModeLv: BagModeLvCfgReader;
    private _MiniGameLv: MiniGameLvCfgReader;
    private _dragonPath: dragonPathCfgReader;
    private _PayShop: PayShopCfgReader;
    private _RoleUnlock: RoleUnlockCfgReader;
    private _Role: RoleCfgReader;
    private _Sound: SoundCfgReader;
    private _Task: TaskCfgReader;
    private _TaskType: TaskTypeCfgReader;
    private _ProcessRewards: ProcessRewardsCfgReader;
    private _Tower: TowerCfgReader;
    private _BagBuff: BagBuffCfgReader;
    private _Buff: BuffCfgReader;
    private _TowerCoinReward: TowerCoinRewardCfgReader;
    private _TowerAmethystReward: TowerAmethystRewardCfgReader;
    private _Gameatr: GameatrCfgReader;
    private _Map: MapCfgReader;
    private _dmmRole: dmmRoleCfgReader;
    private _dmmItem: dmmItemCfgReader;
    private _randomName: randomNameCfgReader;
    private _TowerMenu: TowerMenuCfgReader;
    private _Monster: MonsterCfgReader;
    private _MonsterLv: MonsterLvCfgReader;
    private _bagMonsterLv: bagMonsterLvCfgReader;
    private _Skiilpool: SkiilpoolCfgReader;
    private _BuildModeSkiilpool: BuildModeSkiilpoolCfgReader;
    private _BagModeSkillPool: BagModeSkillPoolCfgReader;
    private _PoolList: PoolListCfgReader;
    private _BagSkill: BagSkillCfgReader;
    private _Skill: SkillCfgReader;
    private _Weather: WeatherCfgReader;
    
    public keyjs: any = {};
    public keyJson: any = {
        activity: 1,
        adReward: 1,
        BagShopItem: 1,
        LvOutside: 1,
        LvInside: 1,
        BulletEffect: 1,
        CurrencyConfig: 1,
        sign: 1,
        DropConfig: 1,
        RoleLv: 1,
        TowerLv: 1,
        EquipLv: 1,
        EquipMergeLv: 1,
        MiniGameEquip: 1,
        GameSetting: 1,
        BagGuide: 1,
        Guide: 1,
        language: 1,
        LevelExp: 1,
        BoxLevelExp: 1,
        BagModeLv: 1,
        MiniGameLv: 1,
        dragonPath: 1,
        PayShop: 1,
        RoleUnlock: 1,
        Role: 1,
        Sound: 1,
        Task: 1,
        TaskType: 1,
        ProcessRewards: 1,
        Tower: 1,
        BagBuff: 1,
        Buff: 1,
        TowerCoinReward: 1,
        TowerAmethystReward: 1,
        Gameatr: 1,
        Map: 1,
        dmmRole: 1,
        dmmItem: 1,
        randomName: 1,
        TowerMenu: 1,
        Monster: 1,
        MonsterLv: 1,
        bagMonsterLv: 1,
        Skiilpool: 1,
        BuildModeSkiilpool: 1,
        BagModeSkillPool: 1,
        PoolList: 1,
        BagSkill: 1,
        Skill: 1,
        Weather: 1
    };

    constructor() {
        this._activity = new activityCfgReader();
        this._adReward = new adRewardCfgReader();
        this._BagShopItem = new BagShopItemCfgReader();
        this._LvOutside = new LvOutsideCfgReader();
        this._LvInside = new LvInsideCfgReader();
        this._BulletEffect = new BulletEffectCfgReader();
        this._CurrencyConfig = new CurrencyConfigCfgReader();
        this._sign = new signCfgReader();
        this._DropConfig = new DropConfigCfgReader();
        this._RoleLv = new RoleLvCfgReader();
        this._TowerLv = new TowerLvCfgReader();
        this._EquipLv = new EquipLvCfgReader();
        this._EquipMergeLv = new EquipMergeLvCfgReader();
        this._MiniGameEquip = new MiniGameEquipCfgReader();
        this._GameSetting = new GameSettingCfgReader();
        this._BagGuide = new BagGuideCfgReader();
        this._Guide = new GuideCfgReader();
        this._language = new languageCfgReader();
        this._LevelExp = new LevelExpCfgReader();
        this._BoxLevelExp = new BoxLevelExpCfgReader();
        this._BagModeLv = new BagModeLvCfgReader();
        this._MiniGameLv = new MiniGameLvCfgReader();
        this._dragonPath = new dragonPathCfgReader();
        this._PayShop = new PayShopCfgReader();
        this._RoleUnlock = new RoleUnlockCfgReader();
        this._Role = new RoleCfgReader();
        this._Sound = new SoundCfgReader();
        this._Task = new TaskCfgReader();
        this._TaskType = new TaskTypeCfgReader();
        this._ProcessRewards = new ProcessRewardsCfgReader();
        this._Tower = new TowerCfgReader();
        this._BagBuff = new BagBuffCfgReader();
        this._Buff = new BuffCfgReader();
        this._TowerCoinReward = new TowerCoinRewardCfgReader();
        this._TowerAmethystReward = new TowerAmethystRewardCfgReader();
        this._Gameatr = new GameatrCfgReader();
        this._Map = new MapCfgReader();
        this._dmmRole = new dmmRoleCfgReader();
        this._dmmItem = new dmmItemCfgReader();
        this._randomName = new randomNameCfgReader();
        this._TowerMenu = new TowerMenuCfgReader();
        this._Monster = new MonsterCfgReader();
        this._MonsterLv = new MonsterLvCfgReader();
        this._bagMonsterLv = new bagMonsterLvCfgReader();
        this._Skiilpool = new SkiilpoolCfgReader();
        this._BuildModeSkiilpool = new BuildModeSkiilpoolCfgReader();
        this._BagModeSkillPool = new BagModeSkillPoolCfgReader();
        this._PoolList = new PoolListCfgReader();
        this._BagSkill = new BagSkillCfgReader();
        this._Skill = new SkillCfgReader();
        this._Weather = new WeatherCfgReader();
    }

    get activity() { return this._activity; }
    get adReward() { return this._adReward; }
    get BagShopItem() { return this._BagShopItem; }
    get LvOutside() { return this._LvOutside; }
    get LvInside() { return this._LvInside; }
    get BulletEffect() { return this._BulletEffect; }
    get CurrencyConfig() { return this._CurrencyConfig; }
    get sign() { return this._sign; }
    get DropConfig() { return this._DropConfig; }
    get RoleLv() { return this._RoleLv; }
    get TowerLv() { return this._TowerLv; }
    get EquipLv() { return this._EquipLv; }
    get EquipMergeLv() { return this._EquipMergeLv; }
    get MiniGameEquip() { return this._MiniGameEquip; }
    get GameSetting() { return this._GameSetting; }
    get BagGuide() { return this._BagGuide; }
    get Guide() { return this._Guide; }
    get language() { return this._language; }
    get LevelExp() { return this._LevelExp; }
    get BoxLevelExp() { return this._BoxLevelExp; }
    get BagModeLv() { return this._BagModeLv; }
    get MiniGameLv() { return this._MiniGameLv; }
    get dragonPath() { return this._dragonPath; }
    get PayShop() { return this._PayShop; }
    get RoleUnlock() { return this._RoleUnlock; }
    get Role() { return this._Role; }
    get Sound() { return this._Sound; }
    get Task() { return this._Task; }
    get TaskType() { return this._TaskType; }
    get ProcessRewards() { return this._ProcessRewards; }
    get Tower() { return this._Tower; }
    get BagBuff() { return this._BagBuff; }
    get Buff() { return this._Buff; }
    get TowerCoinReward() { return this._TowerCoinReward; }
    get TowerAmethystReward() { return this._TowerAmethystReward; }
    get Gameatr() { return this._Gameatr; }
    get Map() { return this._Map; }
    get dmmRole() { return this._dmmRole; }
    get dmmItem() { return this._dmmItem; }
    get randomName() { return this._randomName; }
    get TowerMenu() { return this._TowerMenu; }
    get Monster() { return this._Monster; }
    get MonsterLv() { return this._MonsterLv; }
    get bagMonsterLv() { return this._bagMonsterLv; }
    get Skiilpool() { return this._Skiilpool; }
    get BuildModeSkiilpool() { return this._BuildModeSkiilpool; }
    get BagModeSkillPool() { return this._BagModeSkillPool; }
    get PoolList() { return this._PoolList; }
    get BagSkill() { return this._BagSkill; }
    get Skill() { return this._Skill; }
    get Weather() { return this._Weather; }

    async initBySingleJson(): Promise<any> {
        return new Promise((resolve, reject) => {
            cc.resources.loadDir("config", (err: any, assets: any[]) => {
                if (err) {
                    cc.error("Cfg.initBySingleJson error", err);
                    return reject();
                }
                for (let i = 0; i < assets.length; i++) {
                    const asset = assets[i];
                    const name = asset.name;
                    if (this.hasOwnProperty("_" + name)) {
                        (this as any)["_" + name].initByMap(asset.json);
                    } else {
                        cc.warn("Cfg.initBySingleJson null, " + name);
                    }
                }
                resolve(null);
            });
        });
    }

    HasTag(obj: any, tag: string): boolean {
        return obj.tags != null && obj.tags.indexOf(tag) >= 0;
    }

    selectArray(array: any, index: any, subIndex: any, defaultValue: any): any {
        const item = array[index];
        if (item == null) {
            return defaultValue;
        } else {
            return item[subIndex] || defaultValue;
        }
    }
}

export const Cfg = new CfgManager();
