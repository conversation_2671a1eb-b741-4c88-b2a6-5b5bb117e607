import { GRID_TYPE } from "./GridView";

export class AutoScaleComponent {
    public parentSize: cc.Vec2 = cc.Vec2.ZERO;
    public itemSize: cc.Vec2 = cc.Vec2.ZERO;
    public space: cc.Vec2 = cc.Vec2.ZERO;
    public keyCount: number = 0;
    public type: GRID_TYPE;

    constructor() {
        this.parentSize = cc.Vec2.ZERO;
        this.itemSize = cc.Vec2.ZERO;
        this.space = cc.Vec2.ZERO;
        this.keyCount = 0;
    }

    getScale(): number {
        let scale = 1;
        let itemDimension = 0;
        let spaceDimension = 0;
        let parentDimension = 0;

        switch (this.type) {
            case GRID_TYPE.GRID_VERTICAL:
                itemDimension = this.itemSize.x;
                spaceDimension = this.space.x;
                parentDimension = this.parentSize.x;
                break;
            case GRID_TYPE.GRID_HORIZONTAL:
                itemDimension = this.itemSize.y;
                spaceDimension = this.space.y;
                parentDimension = this.parentSize.y;
                break;
        }

        const totalItemSize = itemDimension * this.keyCount;
        const totalSpaceSize = spaceDimension * (this.keyCount - 1);
        
        if (totalItemSize + totalSpaceSize > parentDimension) {
            scale = (parentDimension - totalSpaceSize) / this.keyCount / itemDimension;
        }

        return scale;
    }
}
