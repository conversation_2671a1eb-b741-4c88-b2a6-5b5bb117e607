// GTSimpleSpriteAssembler2D.ts
// 从 GTSimpleSpriteAssembler2D.js 转换而来

import GTAssembler2D from "./GTAssembler2D";

/**
 * GTSimpleSpriteAssembler2D类
 * 简单精灵装配器2D，继承自GTAssembler2D
 */
export default class GTSimpleSpriteAssembler2D extends GTAssembler2D {
    
    /**
     * 更新渲染数据
     * @param sprite 精灵组件
     */
    updateRenderData(sprite: any): void {
        this.packToDynamicAtlas(sprite, sprite.spriteFrame);
        super.updateRenderData(sprite);
    }

    /**
     * 更新UV坐标
     * @param sprite 精灵组件
     */
    updateUVs(sprite: any): void {
        const uv = sprite._spriteFrame.uv;
        const uvOffset = this.uvOffset;
        const floatsPerVert = this.floatsPerVert;
        const vData = this._renderData.vDatas[0];
        
        for (let i = 0; i < 4; i++) {
            const uvIndex = 2 * i;
            const vertIndex = floatsPerVert * i + uvOffset;
            vData[vertIndex] = uv[uvIndex];
            vData[vertIndex + 1] = uv[uvIndex + 1];
        }
    }

    /**
     * 更新顶点数据
     * @param sprite 精灵组件
     */
    updateVerts(sprite: any): void {
        let left: number, bottom: number, right: number, top: number;
        
        const node = sprite.node;
        const width = node.width;
        const height = node.height;
        const anchorX = node.anchorX * width;
        const anchorY = node.anchorY * height;
        
        if (sprite.trim) {
            left = -anchorX;
            bottom = -anchorY;
            right = width - anchorX;
            top = height - anchorY;
        } else {
            const spriteFrame = sprite.spriteFrame;
            const originalWidth = spriteFrame._originalSize.width;
            const originalHeight = spriteFrame._originalSize.height;
            const rectWidth = spriteFrame._rect.width;
            const rectHeight = spriteFrame._rect.height;
            const offset = spriteFrame._offset;
            const scaleX = width / originalWidth;
            const scaleY = height / originalHeight;
            const trimLeft = offset.x + (originalWidth - rectWidth) / 2;
            const trimRight = offset.x - (originalWidth - rectWidth) / 2;
            
            left = trimLeft * scaleX - anchorX;
            bottom = (offset.y + (originalHeight - rectHeight) / 2) * scaleY - anchorY;
            right = width + trimRight * scaleX - anchorX;
            top = height + (offset.y - (originalHeight - rectHeight) / 2) * scaleY - anchorY;
        }
        
        const local = this._local;
        local[0] = left;
        local[1] = bottom;
        local[2] = right;
        local[3] = top;
        
        this.updateWorldVerts(sprite);
    }
}
