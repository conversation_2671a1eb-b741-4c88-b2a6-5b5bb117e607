import { TConfig } from "./TConfig";

export enum GameatrDefine {
    hp = 1,
    recover = 2,
    roleatk = 3,
    cirtdam = 4,
    cirtrate = 5,
    hitrate = 6,
    dodge = 7,
    movespeed = 8,
    exp = 9,
    itemarea = 10,
    roledef = 11,
    invincible = 12,
    modeScale = 13,
    monsterOffset = 14,
    wokermovespeed = 15,
    spirit = 16,
    skilldam = 100,
    barrageNum = 101,
    scale = 102,
    cd = 103,
    dur = 104,
    damcd = 105,
    freeze = 106,
    rotate = 107,
    combo = 108,
    buffEffect = 109,
    attackarea = 110,
    slayingDamage_30 = 111,
    camera = 112,
    damadd = 113,
    damdis = 114,
    beheaded = 115,
    dropWood = 116,
    getCoinRate = 117,
    woodChange = 118,
    repel = 119,
    penNum = 120,
    shieldPer = 121,
    buffSubSkill = 122,
    roundReward = 123,
    buffDam = 124,
    hpPer = 125,
    shieldBlock = 126,
    deepHurt = 127,
    bulletTime = 128,
    buffOverlay = 129,
    ReboundDamPer = 130,
    ReboundDamVal = 131,
    addshieldper = 132,
    barrageSpeed = 133,
    repeled = 134,
    replaceBullet = 135,
    vampireEffect = 136,
    sheildLimit = 137,
    buffWeight = 138,
    replaceSkill = 139,
    replaceBuff = 140,
    useSkill = 141,
    addObjectBuff = 142,
    addSkill = 143,
    areaDam = 144,
    buffDur = 145,
    resistDam = 146,
    buffEffectAdd = 147,
    skillCrit = 148,
    skillCritNum = 149,
    otherValueAdd = 150,
    replaceRole = 151,
    buffWeightChange = 152,
    subSkillTrigger = 153,
    normal = 200,
    fire = 201,
    ice = 202,
    electricity = 203,
    wind = 204,
    monsterSplit = 300,
    monsterAppear = 301,
    addMana = 400,
    hpval = 401,
    recoverval = 402,
    roleatkval = 403,
    cirtdamval = 404,
    ouputManaval = 406,
    skillcdval = 407,
    shieldval = 408,
    reflexCount = 500,
    lastAttackCrit = 501,
    disarm = 502,
    bagGridCd = 1000,
    bagGridDam = 1001,
    bagGridAppear = 1002,
    bagGridCritRate = 1003,
    bagGridCritDam = 1004,
    targetequip = 2000,
    cdequip = 2001,
    atkequip = 2002,
    hpequip = 2003,
    rangeequip = 2004,
    healequip = 2005,
    shieldequip = 2006,
    silvercoinequip = 2007,
    healeratequip = 2008,
    alldam = 3000,
    allhp = 3001
}

export class GameatrCfgReader extends TConfig {
    protected _name: string = "Gameatr";
}
