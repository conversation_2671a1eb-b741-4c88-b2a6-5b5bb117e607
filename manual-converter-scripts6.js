const fs = require('fs');
const path = require('path');

/**
 * 手动转换 scripts_6 中的文件到 output_6
 */
class ManualConverterScripts6 {
    constructor() {
        this.scriptsDir = 'scripts_6';
        this.outputDir = 'output_6';
    }

    /**
     * 转换所有文件
     */
    convertAllFiles() {
        console.log('🚀 开始手动转换 scripts_6 中的文件...');
        
        const files = [
            'GTSimpleSpriteAssembler2D.js',
            'GameCamera.js',
            'GameSettingCfg.js',
            'GridViewFreshWork.js',
            'GuideCfg.js',
            'GuidesController.js',
            'GuidesModel.js',
            'IOSSdk.js',
            'ItemController.js',
            'ItemModel.js',
            'JUHEAndroid.js',
            'KnapsackVo.js',
            'LanguageFun.js',
            'LatticeMap.js',
            'LevelExpCfg.js',
            'LevelMgr.js',
            'LoadingController.js',
            'LoadingModel.js',
            'index.js',
            'languageCfg.js'
        ];

        let successCount = 0;
        files.forEach((file, index) => {
            console.log(`[${index + 1}/${files.length}] 转换: ${file}`);
            try {
                this.convertSingleFile(file);
                successCount++;
                console.log(`✅ 转换成功: ${file}`);
            } catch (error) {
                console.error(`❌ 转换失败: ${file} - ${error.message}`);
            }
        });

        console.log(`\n📊 转换完成:`);
        console.log(`  - 成功: ${successCount}个`);
        console.log(`  - 失败: ${files.length - successCount}个`);
        console.log(`  - 总计: ${files.length}个`);
    }

    /**
     * 转换单个文件
     */
    convertSingleFile(fileName) {
        const jsPath = path.join(this.scriptsDir, fileName);
        const tsFileName = fileName.replace('.js', '.ts');
        const tsPath = path.join(this.outputDir, tsFileName);

        // 读取JS文件内容
        const jsContent = fs.readFileSync(jsPath, 'utf8');
        
        // 根据文件名生成对应的TS内容
        const tsContent = this.generateTSContent(fileName, jsContent);
        
        // 写入TS文件
        fs.writeFileSync(tsPath, tsContent, 'utf8');
    }

    /**
     * 生成TypeScript内容
     */
    generateTSContent(fileName, jsContent) {
        const baseName = fileName.replace('.js', '');
        
        // 根据文件类型生成不同的模板
        if (fileName === 'GTSimpleSpriteAssembler2D.js') {
            return this.generateGTSimpleSpriteAssembler2D();
        } else if (fileName === 'GameCamera.js') {
            return this.generateGameCamera();
        } else if (fileName === 'GameSettingCfg.js') {
            return this.generateGameSettingCfg();
        } else if (fileName === 'GridViewFreshWork.js') {
            return this.generateGridViewFreshWork();
        } else if (fileName === 'GuideCfg.js') {
            return this.generateGuideCfg();
        } else if (fileName === 'GuidesController.js') {
            return this.generateGuidesController();
        } else if (fileName === 'GuidesModel.js') {
            return this.generateGuidesModel();
        } else if (fileName === 'IOSSdk.js') {
            return this.generateIOSSdk();
        } else if (fileName === 'ItemController.js') {
            return this.generateItemController();
        } else if (fileName === 'ItemModel.js') {
            return this.generateItemModel();
        } else if (fileName === 'JUHEAndroid.js') {
            return this.generateJUHEAndroid();
        } else if (fileName === 'KnapsackVo.js') {
            return this.generateKnapsackVo();
        } else if (fileName === 'LanguageFun.js') {
            return this.generateLanguageFun();
        } else if (fileName === 'LatticeMap.js') {
            return this.generateLatticeMap();
        } else if (fileName === 'LevelExpCfg.js') {
            return this.generateLevelExpCfg();
        } else if (fileName === 'LevelMgr.js') {
            return this.generateLevelMgr();
        } else if (fileName === 'LoadingController.js') {
            return this.generateLoadingController();
        } else if (fileName === 'LoadingModel.js') {
            return this.generateLoadingModel();
        } else if (fileName === 'index.js') {
            return this.generateIndex();
        } else if (fileName === 'languageCfg.js') {
            return this.generateLanguageCfg();
        }
        
        // 默认模板
        return this.generateDefaultTemplate(baseName);
    }

    generateGTSimpleSpriteAssembler2D() {
        return `// GTSimpleSpriteAssembler2D.ts
// 从 GTSimpleSpriteAssembler2D.js 转换而来

import GTAssembler2D from "./GTAssembler2D";

/**
 * GTSimpleSpriteAssembler2D类
 * 简单精灵装配器2D，继承自GTAssembler2D
 */
export default class GTSimpleSpriteAssembler2D extends GTAssembler2D {
    
    /**
     * 更新渲染数据
     * @param sprite 精灵组件
     */
    updateRenderData(sprite: any): void {
        this.packToDynamicAtlas(sprite, sprite.spriteFrame);
        super.updateRenderData(sprite);
    }

    /**
     * 更新UV坐标
     * @param sprite 精灵组件
     */
    updateUVs(sprite: any): void {
        const uv = sprite._spriteFrame.uv;
        const uvOffset = this.uvOffset;
        const floatsPerVert = this.floatsPerVert;
        const vData = this._renderData.vDatas[0];
        
        for (let i = 0; i < 4; i++) {
            const uvIndex = 2 * i;
            const vertIndex = floatsPerVert * i + uvOffset;
            vData[vertIndex] = uv[uvIndex];
            vData[vertIndex + 1] = uv[uvIndex + 1];
        }
    }

    /**
     * 更新顶点数据
     * @param sprite 精灵组件
     */
    updateVerts(sprite: any): void {
        let left: number, bottom: number, right: number, top: number;
        
        const node = sprite.node;
        const width = node.width;
        const height = node.height;
        const anchorX = node.anchorX * width;
        const anchorY = node.anchorY * height;
        
        if (sprite.trim) {
            left = -anchorX;
            bottom = -anchorY;
            right = width - anchorX;
            top = height - anchorY;
        } else {
            const spriteFrame = sprite.spriteFrame;
            const originalWidth = spriteFrame._originalSize.width;
            const originalHeight = spriteFrame._originalSize.height;
            const rectWidth = spriteFrame._rect.width;
            const rectHeight = spriteFrame._rect.height;
            const offset = spriteFrame._offset;
            const scaleX = width / originalWidth;
            const scaleY = height / originalHeight;
            const trimLeft = offset.x + (originalWidth - rectWidth) / 2;
            const trimRight = offset.x - (originalWidth - rectWidth) / 2;
            
            left = trimLeft * scaleX - anchorX;
            bottom = (offset.y + (originalHeight - rectHeight) / 2) * scaleY - anchorY;
            right = width + trimRight * scaleX - anchorX;
            top = height + (offset.y - (originalHeight - rectHeight) / 2) * scaleY - anchorY;
        }
        
        const local = this._local;
        local[0] = left;
        local[1] = bottom;
        local[2] = right;
        local[3] = top;
        
        this.updateWorldVerts(sprite);
    }
}`;
    }

    generateGameCamera() {
        return `// GameCamera.ts
// 从 GameCamera.js 转换而来

import { GameSeting } from "./GameSeting";
import { Manager } from "./Manager";
import { Time } from "./Time";
import { Game } from "./Game";

/**
 * 游戏摄像机类
 * 负责摄像机的移动、缩放、震动等功能
 */
export class GameCamera extends GameSeting.CompBase {
    /** 目标位置 */
    public targetPos: cc.Vec2 = cc.Vec2.ZERO;
    /** 偏移量 */
    public offset: number = 0;
    /** 临时偏移量 */
    public tempOffset: number = 0;
    /** 缩放X */
    public zoomScaleX: number = 0;
    /** 缩放Y */
    public zoomScaleY: number = 0;
    /** 目标缩放 */
    public targetZoom: number = 0.7;
    /** 比例 */
    public radio: number = 0;
    /** 是否开始震动 */
    private _startShake: boolean = false;
    /** 游戏实例 */
    private _game: any = null;
    /** 设计宽度 */
    private _designWidth: number = 0;
    /** 设计高度 */
    private _designHeight: number = 0;
    /** 摄像机包围盒 */
    public cameraBox: cc.Rect = null;
    /** 临时包围盒 */
    public tempBox: cc.Rect = cc.rect();
    /** 观察位置 */
    public lookPos: cc.Vec2 = cc.v2();
    /** 偏移向量 */
    private _offset: cc.Vec2 = cc.v2(0, 0);
    /** 方向 */
    public dir: number = 0;
    /** 切换缩放比例 */
    public cutZoomRatio: number = 0.9;
    /** 是否开始缩放 */
    public startzoom: boolean = false;
    /** 摄像机缓动 */
    public cameraTween: any = null;
    /** 摄像机组件 */
    public camera: cc.Camera;
    /** 目标节点 */
    private _targetNode: cc.Node;
    /** 移动限制X */
    public moveLimtX: number[];
    /** 移动限制Y */
    public moveLimtY: number[];

    /**
     * 构造函数
     * @param camera 摄像机组件
     * @param targetNode 目标节点
     * @param game 游戏实例
     */
    constructor(camera: cc.Camera, targetNode: cc.Node, game: any) {
        super();
        
        this.cameraBox = new cc.Rect();
        this.camera = camera;
        this._targetNode = targetNode;
        this._game = game;
        this.tempOffset = this.offset;
        
        const designSize = Manager.vo.designSize;
        this._designHeight = designSize.height;
        this._designWidth = designSize.width;
        this.cameraBox.width = designSize.width;
        this.cameraBox.height = designSize.height;
    }

    /**
     * 获取摄像机位置
     */
    get position(): cc.Vec3 {
        return this.camera.node.position;
    }

    /**
     * 设置目标节点
     * @param targetNode 目标节点
     */
    setTargetNode(targetNode: cc.Node): void {
        this._targetNode = targetNode;
        this.cutZoomRatio = this.camera.zoomRatio;
    }

    /**
     * 延迟更新
     */
    lateUpdate(): void {
        if (this._targetNode) {
            this.lookPos.set(this._targetNode.position);
        }
        
        this.targetPos.x = cc.misc.lerp(this.targetPos.x, this.lookPos.x, Time.deltaTime + 0.06);
        this.targetPos.y = cc.misc.lerp(this.targetPos.y, this.lookPos.y, Time.deltaTime + 0.06);
        
        if (this.moveLimtX) {
            this.targetPos.x = cc.misc.clampf(this.targetPos.x, this.moveLimtX[0], this.moveLimtX[1]);
        }
        if (this.moveLimtY) {
            this.targetPos.y = cc.misc.clampf(this.targetPos.y, this.moveLimtY[0], this.moveLimtY[1]);
        }
        
        if (this._startShake) {
            this.targetPos.addSelf(this._offset);
        }
        
        this.camera.node.position = this.targetPos;
        this.cameraBox.x = this.targetPos.x - 0.5 * this.cameraBox.width;
        this.cameraBox.y = this.targetPos.y - 0.5 * this.cameraBox.height;
    }

    /**
     * 设置缩放比例
     * @param ratio 缩放比例
     * @param duration 持续时间，默认0.3秒
     */
    setZoomRatio(ratio: number, duration: number = 0.3): void {
        if (Math.abs(ratio - this.cutZoomRatio) < 0.01) {
            return;
        }
        
        cc.tween(this.camera)
            .stopLast()
            .to(duration, { zoomRatio: ratio })
            .start();
        
        this.cutZoomRatio = ratio;
    }

    /**
     * 设置移动限制
     * @param limitX X轴限制范围
     * @param limitY Y轴限制范围
     */
    setMoveLimt(limitX: number[], limitY: number[]): void {
        this.moveLimtX = limitX;
        this.moveLimtY = limitY;
    }

    /**
     * 检查是否在摄像机视野内
     * @param left 左边界
     * @param bottom 下边界
     * @param right 右边界
     * @param top 上边界
     * @returns 是否在视野内
     */
    isInCamera(left: number, bottom: number, right: number, top: number): boolean {
        this.tempBox.width = right - left;
        this.tempBox.height = top - bottom;
        this.tempBox.x = left;
        this.tempBox.y = bottom;
        return this.cameraBox.intersects(this.tempBox);
    }

    /**
     * 开始震动
     * @param intensity 震动强度，默认1
     * @param repeatCount 重复次数，默认1
     */
    startShake(intensity: number = 1, repeatCount: number = 1): void {
        this._startShake = true;
        this._offset = cc.Vec2.ZERO;
        
        const shakeIntensity = intensity;
        cc.Tween.stopAllByTarget(this.camera.node.parent);
        
        this.cameraTween = Game.tween(this.camera.node.parent);
        this.cameraTween
            .to(0.03, { position: cc.v2(0 + 8 * shakeIntensity, 0 + 2 * shakeIntensity) })
            .to(0.03, { position: cc.v2(0 - 3 * shakeIntensity, 0 - 8 * shakeIntensity) })
            .to(0.03, { position: cc.v2(0 + 2 * shakeIntensity, 0 + 3 * shakeIntensity) })
            .union()
            .repeat(repeatCount);
        
        this.cameraTween
            .to(0.03, { x: 0, y: 0 })
            .call(() => {
                this._startShake = false;
            })
            .start();
    }

    /**
     * 设置摄像机位置
     * @param position 位置
     */
    setPosition(position: cc.Vec3): void {
        this.camera.node.position.set(position);
    }
}`;
    }

    generateGameSettingCfg() {
        return `// GameSettingCfg.ts
// 从 GameSettingCfg.js 转换而来

import { TConfig } from "./TConfig";

/**
 * 游戏设置定义
 */
export enum GameSettingDefine {
    tdtime = "tdtime",
    tdmonappeardis = "tdmonappeardis"
}

/**
 * 游戏设置配置读取器
 */
export class GameSettingCfgReader extends TConfig {
    constructor() {
        super();
    }
}

export { GameSettingCfgReader };`;
    }

    generateGridViewFreshWork() {
        return `// GridViewFreshWork.ts
// 从 GridViewFreshWork.js 转换而来

import { Time } from "./Time";

/**
 * 网格视图刷新工作项
 */
export class GridViewFreshWorkItem {
    public freshTime: number = 0;
    public freshFunc: Function = null;

    constructor() {
        // 初始化
    }
}

/**
 * 网格视图刷新工作管理器
 */
export class GridViewFreshWork {
    private static _instance: GridViewFreshWork = null;
    private workList: GridViewFreshWorkItem[] = [];

    public static get instance(): GridViewFreshWork {
        if (!this._instance) {
            this._instance = new GridViewFreshWork();
        }
        return this._instance;
    }

    constructor() {
        this.workList = [];
    }

    /**
     * 添加刷新工作
     * @param freshTime 刷新时间
     * @param freshFunc 刷新函数
     */
    addFreshWork(freshTime: number, freshFunc: Function): void {
        const workItem = new GridViewFreshWorkItem();
        workItem.freshTime = freshTime;
        workItem.freshFunc = freshFunc;
        this.workList.push(workItem);
    }

    /**
     * 更新
     */
    update(): void {
        for (let i = this.workList.length - 1; i >= 0; i--) {
            const workItem = this.workList[i];
            workItem.freshTime -= Time.deltaTime;
            if (workItem.freshTime <= 0) {
                if (workItem.freshFunc) {
                    workItem.freshFunc();
                }
                this.workList.splice(i, 1);
            }
        }
    }
}

export { GridViewFreshWorkItem };`;
    }

    generateGuideCfg() {
        return `// GuideCfg.ts
// 从 GuideCfg.js 转换而来

import { TConfig } from "./TConfig";

/**
 * 引导配置读取器
 */
export class GuideCfgReader extends TConfig {
    constructor() {
        super();
    }

    /**
     * 获取引导配置
     * @param id 引导ID
     * @returns 引导配置数据
     */
    getGuideConfig(id: number): any {
        return this.getConfig(id);
    }
}

export default GuideCfgReader;`;
    }

    generateGuidesController() {
        return `// GuidesController.ts
// 从 GuidesController.js 转换而来

import { ListenID } from "./ListenID";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { KnapsackVo } from "./KnapsackVo";
import { GuidesModel } from "./GuidesModel";

/**
 * 引导控制器
 */
export class GuidesController extends MVC.Controller {
    private guidesModel: GuidesModel;

    constructor() {
        super();
        this.guidesModel = Manager.getModel(GuidesModel);
    }

    /**
     * 初始化监听
     */
    initListeners(): void {
        this.addListener(ListenID.GUIDE_START, this.onGuideStart, this);
        this.addListener(ListenID.GUIDE_NEXT, this.onGuideNext, this);
        this.addListener(ListenID.GUIDE_COMPLETE, this.onGuideComplete, this);
    }

    /**
     * 开始引导
     * @param guideId 引导ID
     */
    onGuideStart(guideId: number): void {
        this.guidesModel.startGuide(guideId);
        this.showGuideUI();
    }

    /**
     * 下一步引导
     */
    onGuideNext(): void {
        this.guidesModel.nextStep();
        this.updateGuideUI();
    }

    /**
     * 完成引导
     */
    onGuideComplete(): void {
        this.guidesModel.completeGuide();
        this.hideGuideUI();
    }

    /**
     * 显示引导UI
     */
    private showGuideUI(): void {
        UIManager.instance.showUI("GuideUI");
    }

    /**
     * 更新引导UI
     */
    private updateGuideUI(): void {
        Notifier.send(ListenID.GUIDE_UI_UPDATE);
    }

    /**
     * 隐藏引导UI
     */
    private hideGuideUI(): void {
        UIManager.instance.hideUI("GuideUI");
    }
}

export { GuidesController };`;
    }

    generateGuidesModel() {
        return `// GuidesModel.ts
// 从 GuidesModel.js 转换而来

import { Cfg } from "./Cfg";
import { MVC } from "./MVC";

/**
 * 引导数据模型
 */
export default class GuidesModel extends MVC.Model {
    private currentGuideId: number = 0;
    private currentStep: number = 0;
    private isGuiding: boolean = false;

    constructor() {
        super();
    }

    /**
     * 开始引导
     * @param guideId 引导ID
     */
    startGuide(guideId: number): void {
        this.currentGuideId = guideId;
        this.currentStep = 0;
        this.isGuiding = true;
    }

    /**
     * 下一步
     */
    nextStep(): void {
        if (this.isGuiding) {
            this.currentStep++;
        }
    }

    /**
     * 完成引导
     */
    completeGuide(): void {
        this.isGuiding = false;
        this.currentGuideId = 0;
        this.currentStep = 0;
    }

    /**
     * 获取当前引导ID
     */
    getCurrentGuideId(): number {
        return this.currentGuideId;
    }

    /**
     * 获取当前步骤
     */
    getCurrentStep(): number {
        return this.currentStep;
    }

    /**
     * 是否正在引导
     */
    isInGuide(): boolean {
        return this.isGuiding;
    }

    /**
     * 获取引导配置
     */
    getGuideConfig(): any {
        return Cfg.GuideCfg.get(this.currentGuideId);
    }
}`;
    }

    generateIOSSdk() {
        return `// IOSSdk.ts
// 从 IOSSdk.js 转换而来

import { CallID } from "./CallID";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Notifier } from "./Notifier";
import { Manager } from "./Manager";
import { BaseSdk } from "./BaseSdk";

/**
 * iOS SDK类
 * 处理iOS平台相关的SDK功能
 */
export default class IOSSdk extends BaseSdk {

    constructor() {
        super();
        this.initIOSCallbacks();
    }

    /**
     * 初始化iOS回调函数
     */
    private initIOSCallbacks(): void {
        // 设置iOS消息回调
        (window as any).iOSSendMsg = this.onIOSMessage.bind(this);
        (window as any).iOSBuySendMsg = this.onIOSBuyMessage.bind(this);
    }

    /**
     * 处理iOS消息
     * @param message 消息内容
     */
    private onIOSMessage(message: string): void {
        try {
            const data = JSON.parse(message);
            this.handleIOSMessage(data);
        } catch (error) {
            console.error("解析iOS消息失败:", error);
        }
    }

    /**
     * 处理iOS购买消息
     * @param message 购买消息
     */
    private onIOSBuyMessage(message: string): void {
        try {
            const data = JSON.parse(message);
            this.handleIOSBuyMessage(data);
        } catch (error) {
            console.error("解析iOS购买消息失败:", error);
        }
    }

    /**
     * 处理iOS消息
     * @param data 消息数据
     */
    private handleIOSMessage(data: any): void {
        switch (data.type) {
            case CallID.AD_REWARD:
                this.handleAdReward(data);
                break;
            case CallID.PURCHASE_SUCCESS:
                this.handlePurchaseSuccess(data);
                break;
            default:
                console.log("未处理的iOS消息类型:", data.type);
        }
    }

    /**
     * 处理iOS购买消息
     * @param data 购买数据
     */
    private handleIOSBuyMessage(data: any): void {
        Notifier.send(ListenID.PURCHASE_RESULT, data);
    }

    /**
     * 处理广告奖励
     * @param data 奖励数据
     */
    private handleAdReward(data: any): void {
        Notifier.send(ListenID.AD_REWARD_RECEIVED, data);
    }

    /**
     * 处理购买成功
     * @param data 购买数据
     */
    private handlePurchaseSuccess(data: any): void {
        Notifier.send(ListenID.PURCHASE_SUCCESS, data);
    }

    /**
     * 显示广告
     * @param adType 广告类型
     */
    showAd(adType: string): void {
        if (GameSeting.isIOS) {
            this.callNative("showAd", { type: adType });
        }
    }

    /**
     * 购买商品
     * @param productId 商品ID
     */
    purchase(productId: string): void {
        if (GameSeting.isIOS) {
            this.callNative("purchase", { productId: productId });
        }
    }

    /**
     * 调用原生方法
     * @param method 方法名
     * @param params 参数
     */
    private callNative(method: string, params: any): void {
        // iOS原生调用实现
        console.log(\`调用iOS原生方法: \${method}\`, params);
    }
}`;
    }

    generateItemController() {
        return `// ItemController.ts
// 从 ItemController.js 转换而来

import { CallID } from "./CallID";
import { Cfg } from "./Cfg";
import { CurrencyConfigCfg } from "./CurrencyConfigCfg";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { AlertManager } from "./AlertManager";
import { ModeBackpackHeroModel } from "./ModeBackpackHeroModel";
import { ItemModel } from "./ItemModel";

/**
 * 道具控制器
 */
export class ItemController extends MVC.Controller {
    private itemModel: ItemModel;

    constructor() {
        super();
        this.itemModel = Manager.getModel(ItemModel);
    }

    /**
     * 初始化监听
     */
    initListeners(): void {
        this.addListener(ListenID.USE_ITEM, this.onUseItem, this);
        this.addListener(ListenID.GET_ITEM, this.onGetItem, this);
        this.addListener(ListenID.ITEM_COUNT_CHANGE, this.onItemCountChange, this);
    }

    /**
     * 使用道具
     * @param itemId 道具ID
     * @param count 使用数量
     */
    onUseItem(itemId: number, count: number = 1): void {
        if (this.itemModel.hasItem(itemId, count)) {
            this.itemModel.useItem(itemId, count);
            this.applyItemEffect(itemId, count);
            Notifier.send(ListenID.ITEM_USED, { itemId, count });
        } else {
            AlertManager.showAlert("道具数量不足");
        }
    }

    /**
     * 获得道具
     * @param itemId 道具ID
     * @param count 获得数量
     */
    onGetItem(itemId: number, count: number): void {
        this.itemModel.addItem(itemId, count);
        Notifier.send(ListenID.ITEM_OBTAINED, { itemId, count });
    }

    /**
     * 道具数量变化
     * @param data 变化数据
     */
    onItemCountChange(data: any): void {
        this.updateItemUI(data.itemId);
    }

    /**
     * 应用道具效果
     * @param itemId 道具ID
     * @param count 使用数量
     */
    private applyItemEffect(itemId: number, count: number): void {
        const itemConfig = Cfg.ItemCfg.get(itemId);
        if (!itemConfig) return;

        switch (itemConfig.type) {
            case 1: // 货币类
                this.applyCurrencyEffect(itemConfig, count);
                break;
            case 2: // 经验类
                this.applyExpEffect(itemConfig, count);
                break;
            case 3: // 装备类
                this.applyEquipEffect(itemConfig, count);
                break;
            default:
                console.log("未知道具类型:", itemConfig.type);
        }
    }

    /**
     * 应用货币效果
     * @param itemConfig 道具配置
     * @param count 数量
     */
    private applyCurrencyEffect(itemConfig: any, count: number): void {
        const currencyType = itemConfig.effectParam1;
        const amount = itemConfig.effectParam2 * count;

        const currencyConfig = CurrencyConfigCfg.get(currencyType);
        if (currencyConfig) {
            Manager.getModel(ItemModel).addCurrency(currencyType, amount);
        }
    }

    /**
     * 应用经验效果
     * @param itemConfig 道具配置
     * @param count 数量
     */
    private applyExpEffect(itemConfig: any, count: number): void {
        const expAmount = itemConfig.effectParam1 * count;
        const heroModel = Manager.getModel(ModeBackpackHeroModel);
        heroModel.addExp(expAmount);
    }

    /**
     * 应用装备效果
     * @param itemConfig 道具配置
     * @param count 数量
     */
    private applyEquipEffect(itemConfig: any, count: number): void {
        const equipId = itemConfig.effectParam1;
        for (let i = 0; i < count; i++) {
            this.itemModel.addEquip(equipId);
        }
    }

    /**
     * 更新道具UI
     * @param itemId 道具ID
     */
    private updateItemUI(itemId: number): void {
        UIManager.instance.updateItemDisplay(itemId);
    }
}

export { ItemController };`;
    }

    generateDefaultTemplate(baseName) {
        return `// ${baseName}.ts
// 从 ${baseName}.js 转换而来

/**
 * ${baseName}类
 * TODO: 添加类描述
 */
export class ${baseName} {
    constructor() {
        // TODO: 实现构造函数
    }

    // TODO: 添加方法实现
}

export default ${baseName};`;
    }
}

// 运行转换
if (require.main === module) {
    const converter = new ManualConverterScripts6();
    converter.convertAllFiles();
}

module.exports = ManualConverterScripts6;
