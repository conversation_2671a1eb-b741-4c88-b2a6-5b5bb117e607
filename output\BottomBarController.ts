import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { UIManager } from "./UIManager";
import { default as BottomBarModel } from "./BottomBarModel";

export class BottomBarController extends MVC.MController {
    public viewpath: string = "ui/bottombar/BottomBarView";

    constructor() {
        super();
        this.viewpath = "ui/bottombar/BottomBarView";
        this.setup(BottomBarModel.instance);
        this.changeListener(true);
    }

    reset(): void {}

    get classname(): string {
        return "BottomBarController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.BottomBar_OpenView, this.onOpenView, this);
        Notifier.changeListener(enable, ListenID.BottomBar_SelectGame, this._model.startGame, this);
    }

    onOpenView(selectIndex: number = 2, tabIndex: number = 1): void {
        const openArgs = MVC.openArgs();
        openArgs.setSelect(selectIndex);
        openArgs.setTab(tabIndex);
        UIManager.Open(this.viewpath, openArgs);
    }

    setViewVisible(visible: boolean): void {
        const view = UIManager.getView(this.viewpath);
        if (view) {
            view.node.opacity = visible ? 255 : 0;
        } else if (visible) {
            this.onOpenView();
        }
    }
}
