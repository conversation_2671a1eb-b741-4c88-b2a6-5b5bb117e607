import { MVC } from "./MVC";

export class EaseScaleTransition extends MVC.DefaultTransition {
    private _animInSpeed: number = 0.3;
    private _animOutSpeed: number = 0.15;
    public maskbgOpacity: number = 155;
    private _view: any;

    init(view: any): void {
        this._view = view;
        this.maskbgOpacity = this._view.node.children[0].opacity;
        if (this._view.node.children[1]) {
            this._view.node.children[1].width = this._view.node.width;
            this._view.node.children[1].height = this._view.node.height;
        }
    }

    show(): void {
        this._view.node.active = true;
        if (this._view.node.children[0]) {
            this._view.node.children[0].stopAllActions();
            cc.tween(this._view.node.children[0]).call(() => {
                this._view.node.children[0].opacity = 0;
            }).to(0.15, {
                opacity: this.maskbgOpacity
            }, {
                easing: cc.easing.backOut
            }).start();
        }
        if (this._view.node.children[1]) {
            this._view.node.children[1].stopAllActions();
            cc.tween(this._view.node.children[1]).set({
                scale: 0.1
            }).to(this._animInSpeed, {
                scale: 1
            }, {
                easing: cc.easing.backOut
            }).call(() => {
                this.onFadeInFinish();
            }).start();
        } else {
            setTimeout(() => {
                return this._view.showFinish();
            });
        }
    }

    hide(): void {
        if (this._view.node.children[0]) {
            this._view.node.children[0].stopAllActions();
            cc.tween(this._view.node.children[0]).to(0.15, {
                opacity: 0
            }, {
                easing: cc.easing.backIn
            }).start();
        }
        if (this._view.node.children[1]) {
            this._view.node.children[1].stopAllActions();
            cc.tween(this._view.node.children[1]).to(this._animOutSpeed, {
                scale: 0.1
            }, {
                easing: cc.easing.backIn
            }).call(() => {
                this.onFadeOutFinish();
            }).start();
        } else {
            this._view.node.active = false;
            setTimeout(() => {
                return this._view.hideFinish();
            });
        }
    }

    onFadeInFinish(): void {
        this._view.showFinish();
    }

    onFadeOutFinish(): void {
        this._view.node.active = false;
        this._view.hideFinish();
    }
}
