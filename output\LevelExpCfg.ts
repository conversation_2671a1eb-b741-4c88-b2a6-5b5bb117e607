// LevelExpCfg.ts
// 从 LevelExpCfg.js 转换而来

import { TConfig } from "./TConfig";

/**
 * 等级经验配置读取器
 */
export class LevelExpCfgReader extends TConfig {
    
    constructor() {
        super();
    }
    
    /**
     * 获取等级经验配置
     * @param level 等级
     * @returns 经验配置数据
     */
    getLevelExpConfig(level: number): any {
        return this.getConfig(level);
    }
    
    /**
     * 获取升级所需经验
     * @param level 当前等级
     * @returns 升级所需经验值
     */
    getExpForLevel(level: number): number {
        const config = this.getLevelExpConfig(level);
        return config ? config.exp : 0;
    }
    
    /**
     * 获取等级上限
     * @returns 最大等级
     */
    getMaxLevel(): number {
        const configs = this.getAllConfigs();
        return Math.max(...Object.keys(configs).map(Number));
    }
    
    /**
     * 根据经验值计算等级
     * @param totalExp 总经验值
     * @returns 对应的等级
     */
    calculateLevelByExp(totalExp: number): number {
        let currentLevel = 1;
        let accumulatedExp = 0;
        
        const maxLevel = this.getMaxLevel();
        
        for (let level = 1; level <= maxLevel; level++) {
            const expNeeded = this.getExpForLevel(level);
            if (totalExp >= accumulatedExp + expNeeded) {
                accumulatedExp += expNeeded;
                currentLevel = level + 1;
            } else {
                break;
            }
        }
        
        return Math.min(currentLevel, maxLevel);
    }
    
    /**
     * 获取当前等级的累计经验
     * @param level 等级
     * @returns 累计经验值
     */
    getTotalExpForLevel(level: number): number {
        let totalExp = 0;
        
        for (let i = 1; i < level; i++) {
            totalExp += this.getExpForLevel(i);
        }
        
        return totalExp;
    }
    
    /**
     * 获取等级进度信息
     * @param currentExp 当前经验
     * @returns 等级进度信息
     */
    getLevelProgress(currentExp: number): {
        level: number;
        currentLevelExp: number;
        nextLevelExp: number;
        progress: number;
    } {
        const level = this.calculateLevelByExp(currentExp);
        const totalExpForCurrentLevel = this.getTotalExpForLevel(level);
        const currentLevelExp = currentExp - totalExpForCurrentLevel;
        const nextLevelExp = this.getExpForLevel(level);
        const progress = nextLevelExp > 0 ? currentLevelExp / nextLevelExp : 1;
        
        return {
            level,
            currentLevelExp,
            nextLevelExp,
            progress: Math.min(progress, 1)
        };
    }
}

export default LevelExpCfgReader;
