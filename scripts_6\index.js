Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.minSdk = undefined;
var $2Report = require("Report");
var $2Show = require("Show");
var $2Hide = require("Hide");
var $2Login = require("Login");
var $2Api = require("Api");
var $2TimeManage = require("TimeManage");
var $2config = require("config");
window.wxapi = Object.assign({}, window.tt);
var $2ReportQueue = require("ReportQueue");
window.reportQueue = $2ReportQueue.default;
var $2Params = require("Params");
window.params = $2Params.default;
var $2LocalStorage = require("LocalStorage");
var $2Logger$$1 = require("Logger");
var $2EventModel = require("EventModel");
exports.minSdk = window.wx || window.tt || window.qq || window.qg || window.ks;
var def_index = new (function () {
  function _ctor() {
    this._initFlag = false;
    this._showFlag = false;
    $2LocalStorage.default.removeItem($2TimeManage.default.lockGetFirstServerTime);
    $2Hide.default.hide(function () {
      if (window.reportQueue.len() > 0) {
        window.Logger.info("## 处理退出未上报事件(共计" + window.reportQueue.len() + "条)");
        $2Report.default.reportQueue();
      }
      window.Logger.info("## 退出上报在线时长，当前在线时长为：", $2TimeManage.default.getOnlineTime());
      $2Report.default.online();
      clearInterval(window.wonder_online);
      window.wonder_online = null;
    });
  }
  _ctor.prototype.initParams = function (e) {
    var t = this;
    window.wxapi = exports.minSdk;
    window.tt = exports.minSdk;
    window.reportQueue = $2ReportQueue.default;
    window.Logger = new $2Logger$$1.default($2config.DEFAULT_LOG_LEVEL);
    window.Logger.setLogLevel(e.log_level);
    window.Logger.info("## 初始化渠道参数：", e, window.params);
    window.params.setGameParams(e);
    if (exports.minSdk) {
      var i = exports.minSdk.getLaunchOptionsSync();
      window.Logger.info("## 启动参数：", i.query);
      window.params.setShowParams(i.query);
      var r = (null == i ? undefined : i.group_id) || "";
      window.Logger.info("## 视频ID：", r);
      var a = JSON.parse(JSON.stringify(i.query));
      if ("object" == typeof a) {
        var s = a.promotionid || a.adid || a.aid || a.creative_id || "";
        for (var c in a) {
          ["mid3", "projectid", "promotionid", "requestid"].includes(c) && ($2EventModel.default.instance.userVo[c] = a[c]);
        }
        s.length >= 19 && ($2EventModel.default.instance.userVo.ad_id = s);
        $2EventModel.default.instance.userVo.video_id = r;
      }
    }
    this.initReport();
    this.setInterval();
    $2Show.default.show(function () {
      t.setInterval();
    });
  };
  _ctor.prototype.setInterval = function () {
    if (!window.wonder_online) {
      $2TimeManage.default.setStartClientTime();
      window.wonder_online = setInterval(function () {
        $2TimeManage.default.setEndClientTime();
        window.Logger.info("## 在线时长心跳:", $2TimeManage.default.getOnlineTime());
        $2Report.default.checkReportQueue(false);
      }, $2config.ONLINE_DEFAULT_INTERVAL);
    }
  };
  _ctor.prototype.report = function (e, t) {
    exports.minSdk && $2Report.default.other(e, t);
  };
  _ctor.prototype.initReport = function () {
    if (exports.minSdk) {
      if (window.params.getUserParams("openid")) {
        window.Logger.info("## 检查上次是否有异常退出导致在线时长未上报", $2TimeManage.default.getOnlineTime());
        $2TimeManage.default.getOnlineTime() > 0 && $2Report.default.online();
        window.Logger.info("## 检查上次是否有异常退出导致事件队列未上报", window.reportQueue.len());
        window.reportQueue.len() > 0 && $2Report.default.reportQueue();
        $2Report.default.click();
        $2Report.default.active();
        $2Report.default.userInfo();
      } else {
        window.Logger.info("## 重新调用登录获取openid");
        $2Login.default.login({
          success: function (e) {
            window.Logger.info("## login: ", e);
            window.Logger.info("## app_name: " + window.params.getGameParams("app_name"));
            $2Api.getOpenid({
              code: e.code,
              app_name: window.params.getGameParams("app_name")
            }, function (e) {
              if (0 == e.data.code) {
                window.Logger.info("## 获取openid成功 ：", e, e.data.data.openid);
                window.params.setUserParams({
                  openid: e.data.data.openid
                });
                if ($2TimeManage.default.getOnlineTime() > 0) {
                  window.Logger.info("## 处理异常退出导致未上报的在线时长"), $2Report.default.online();
                }
                if (window.reportQueue.len() > 0) {
                  window.Logger.info("## 处理异常退出导致未上报的事件队列"), $2Report.default.reportQueue();
                }
                $2Report.default.click();
                $2Report.default.active();
                $2Report.default.userInfo();
              } else {
                window.Logger.warn("## 获取openid失败，返回参数为：", e.data);
              }
            }, function (e) {
              window.Logger.warn("## 调用获取openid接口失败：", e);
            });
          },
          fail: function (e) {
            window.Logger.warn("调用wx.login失败：", e);
          }
        });
      }
    }
  };
  _ctor.prototype.getReportList = function () {
    return window.reportQueue.getList();
  };
  _ctor.prototype.getFirstReportServerTime = function () {
    return $2TimeManage.default.getFirstReportServerTime();
  };
  _ctor.prototype.getFirstReportClientTime = function () {
    return $2TimeManage.default.getFirstReportClientTime();
  };
  _ctor.prototype.getOnlineTime = function () {
    return $2TimeManage.default.getOnlineTime();
  };
  return _ctor;
}())();
exports.default = def_index;