// LoadingController.ts
// 从 LoadingController.js 转换而来

import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { NotifyID } from "./NotifyID";
import { ListenID } from "./ListenID";
import { UIManager } from "./UIManager";
import { LoadingModel } from "./LoadingModel";

/**
 * 加载控制器
 */
export class LoadingController extends MVC.Controller {
    private loadingModel: LoadingModel;
    
    constructor() {
        super();
        this.loadingModel = new LoadingModel();
    }
    
    /**
     * 初始化监听
     */
    initListeners(): void {
        this.addListener(ListenID.SHOW_LOADING, this.onShowLoading, this);
        this.addListener(ListenID.HIDE_LOADING, this.onHideLoading, this);
        this.addListener(ListenID.UPDATE_LOADING_PROGRESS, this.onUpdateProgress, this);
        this.addListener(ListenID.LOADING_COMPLETE, this.onLoadingComplete, this);
        this.addListener(ListenID.LOADING_ERROR, this.onLoadingError, this);
    }
    
    /**
     * 显示加载界面
     * @param data 加载数据
     */
    onShowLoading(data?: {
        text?: string;
        showProgress?: boolean;
        canCancel?: boolean;
    }): void {
        this.loadingModel.setLoadingState(true);
        this.loadingModel.setLoadingText(data?.text || "加载中...");
        this.loadingModel.setShowProgress(data?.showProgress !== false);
        this.loadingModel.setCanCancel(data?.canCancel || false);
        this.loadingModel.setProgress(0);
        
        // 显示加载UI
        UIManager.instance.showUI("LoadingUI", {
            text: this.loadingModel.getLoadingText(),
            showProgress: this.loadingModel.getShowProgress(),
            canCancel: this.loadingModel.getCanCancel()
        });
        
        // 发送加载开始通知
        Notifier.send(NotifyID.LOADING_STARTED);
    }
    
    /**
     * 隐藏加载界面
     */
    onHideLoading(): void {
        this.loadingModel.setLoadingState(false);
        this.loadingModel.setProgress(100);
        
        // 隐藏加载UI
        UIManager.instance.hideUI("LoadingUI");
        
        // 发送加载结束通知
        Notifier.send(NotifyID.LOADING_ENDED);
    }
    
    /**
     * 更新加载进度
     * @param data 进度数据
     */
    onUpdateProgress(data: {
        progress: number;
        text?: string;
    }): void {
        this.loadingModel.setProgress(data.progress);
        
        if (data.text) {
            this.loadingModel.setLoadingText(data.text);
        }
        
        // 更新UI显示
        this.updateLoadingUI();
        
        // 发送进度更新通知
        Notifier.send(NotifyID.LOADING_PROGRESS_UPDATED, {
            progress: data.progress,
            text: this.loadingModel.getLoadingText()
        });
    }
    
    /**
     * 加载完成
     * @param data 完成数据
     */
    onLoadingComplete(data?: any): void {
        this.loadingModel.setProgress(100);
        this.loadingModel.setLoadingText("加载完成");
        
        // 延迟隐藏加载界面
        setTimeout(() => {
            this.onHideLoading();
        }, 500);
        
        // 发送加载完成通知
        Notifier.send(NotifyID.LOADING_COMPLETED, data);
    }
    
    /**
     * 加载错误
     * @param data 错误数据
     */
    onLoadingError(data: {
        error: string;
        canRetry?: boolean;
    }): void {
        this.loadingModel.setLoadingText(`加载失败: ${data.error}`);
        this.loadingModel.setCanCancel(true);
        
        // 更新UI显示错误状态
        this.updateLoadingUI();
        
        // 发送加载错误通知
        Notifier.send(NotifyID.LOADING_ERROR, data);
        
        // 如果可以重试，显示重试按钮
        if (data.canRetry) {
            this.showRetryOption();
        }
    }
    
    /**
     * 更新加载UI
     */
    private updateLoadingUI(): void {
        const uiData = {
            progress: this.loadingModel.getProgress(),
            text: this.loadingModel.getLoadingText(),
            showProgress: this.loadingModel.getShowProgress(),
            canCancel: this.loadingModel.getCanCancel()
        };
        
        UIManager.instance.updateUI("LoadingUI", uiData);
    }
    
    /**
     * 显示重试选项
     */
    private showRetryOption(): void {
        UIManager.instance.showUI("RetryDialog", {
            message: "加载失败，是否重试？",
            onRetry: () => {
                this.retryLoading();
            },
            onCancel: () => {
                this.cancelLoading();
            }
        });
    }
    
    /**
     * 重试加载
     */
    private retryLoading(): void {
        UIManager.instance.hideUI("RetryDialog");
        
        // 重置加载状态
        this.loadingModel.setProgress(0);
        this.loadingModel.setLoadingText("重新加载中...");
        
        // 发送重试通知
        Notifier.send(NotifyID.LOADING_RETRY);
        
        this.updateLoadingUI();
    }
    
    /**
     * 取消加载
     */
    private cancelLoading(): void {
        UIManager.instance.hideUI("RetryDialog");
        this.onHideLoading();
        
        // 发送取消通知
        Notifier.send(NotifyID.LOADING_CANCELLED);
    }
    
    /**
     * 获取当前加载状态
     */
    getLoadingState(): {
        isLoading: boolean;
        progress: number;
        text: string;
    } {
        return {
            isLoading: this.loadingModel.getLoadingState(),
            progress: this.loadingModel.getProgress(),
            text: this.loadingModel.getLoadingText()
        };
    }
    
    /**
     * 设置加载超时
     * @param timeout 超时时间（毫秒）
     */
    setLoadingTimeout(timeout: number): void {
        setTimeout(() => {
            if (this.loadingModel.getLoadingState()) {
                this.onLoadingError({
                    error: "加载超时",
                    canRetry: true
                });
            }
        }, timeout);
    }
    
    /**
     * 批量加载资源
     * @param resources 资源列表
     * @param onProgress 进度回调
     * @param onComplete 完成回调
     * @param onError 错误回调
     */
    loadResources(
        resources: string[],
        onProgress?: (progress: number, current: string) => void,
        onComplete?: () => void,
        onError?: (error: string) => void
    ): void {
        this.onShowLoading({
            text: "加载资源中...",
            showProgress: true,
            canCancel: false
        });
        
        let loadedCount = 0;
        const totalCount = resources.length;
        
        const loadNext = () => {
            if (loadedCount >= totalCount) {
                onComplete && onComplete();
                this.onLoadingComplete();
                return;
            }
            
            const resource = resources[loadedCount];
            const progress = (loadedCount / totalCount) * 100;
            
            // 更新进度
            this.onUpdateProgress({
                progress: progress,
                text: `加载资源: ${resource}`
            });
            
            onProgress && onProgress(progress, resource);
            
            // 模拟资源加载
            setTimeout(() => {
                loadedCount++;
                loadNext();
            }, 100);
        };
        
        loadNext();
    }
}

export { LoadingController };
