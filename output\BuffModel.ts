import { MVC } from "./MVC";
import { GameUtil } from "./GameUtil";
import { Game, ModeCfg } from "./Game";
import { RewardEvent } from "./RewardEvent";

interface BuffPoolItem {
    id: number;
    isAd: number;
}

interface WeightedItem {
    id: number;
    w: number;
}

export default class BuffModel extends MVC.BaseModel {
    private static _instance: BuffModel = null;
    
    public historyBuffs: number[] = [];
    public curbufflists: number[] = [];
    public isfivecheck: boolean = false;

    constructor() {
        super();
        this.historyBuffs = [];
        this.curbufflists = [];
        this.isfivecheck = false;
        
        if (BuffModel._instance == null) {
            BuffModel._instance = this;
            this.changeListener(true);
        }
    }

    static get instance(): BuffModel {
        if (BuffModel._instance == null) {
            BuffModel._instance = new BuffModel();
        }
        return BuffModel._instance;
    }

    reset(): void {}

    get mode(): BuffModel {
        return BuffModel.instance;
    }

    get game(): any {
        return Game.Mgr.instance;
    }

    get role(): any {
        return this.game?.mainRole;
    }

    changeListener(enable?: boolean): void {
        // Listener implementation
    }

    getBuffPool(
        advCount: number = 1,
        norCount: number = 1,
        type: RewardEvent.Type = RewardEvent.Type.SkillBuff,
        useWeight: boolean = true
    ): BuffPoolItem[] | null {
        const skillPool = ModeCfg.Skiilpool.get(this.game.bronMonsterMgr.level);
        const normalPool: number[] = [];
        const advancedPool: number[] = [];
        let weightedNormal: number[] = [];
        let weightedAdvanced: number[] = [];
        const player = this.role;

        if (!player) {
            return null;
        }

        if (type === RewardEvent.Type.SkillBuff) {
            skillPool.norPassPool.forEach((buffId: number) => {
                if (!player.buffMgr.isHasID([buffId])) {
                    normalPool.push(buffId);
                }
            });
            
            skillPool.advPassPool.forEach((buffId: number) => {
                if (!player.buffMgr.isHasID([buffId])) {
                    advancedPool.push(buffId);
                }
            });
        } else if (type === RewardEvent.Type.Buff) {
            skillPool.norBuffPool.forEach((buffId: number) => {
                if (!player.buffMgr.isHasID([buffId])) {
                    normalPool.push(buffId);
                }
            });
            
            skillPool.advBuffPool.forEach((buffId: number) => {
                if (!player.buffMgr.isHasID([buffId])) {
                    advancedPool.push(buffId);
                }
            });
        }

        weightedNormal = useWeight ? this.addWeight(normalPool, norCount) : normalPool;
        weightedAdvanced = useWeight ? this.addWeight(advancedPool, advCount) : advancedPool;

        const result: BuffPoolItem[] = [];
        
        weightedNormal.forEach((buffId) => {
            result.push({
                id: buffId,
                isAd: 0
            });
        });
        
        weightedAdvanced.forEach((buffId) => {
            result.push({
                id: buffId,
                isAd: 1
            });
        });

        return result;
    }

    addWeight(buffIds: number[], maxCount: number = 999): number[] {
        if (maxCount === 0) {
            return [];
        }

        const weightedItems: WeightedItem[] = [];
        
        for (let i = 0; i < buffIds.length; i++) {
            const buffId = buffIds[i];
            let weight = 100;
            const buffConfig = ModeCfg.Buff.get(buffId);
            
            if (buffConfig) {
                if (buffConfig.type === 3) {
                    if (buffConfig.skillId) {
                        for (let j = 0; j < buffConfig.skillId.length; j++) {
                            if (this.role.skillMgr.hasMainID(buffConfig.skillId[j])) {
                                weight += 80;
                            } else {
                                weight -= 80;
                            }
                        }
                    } else {
                        weight += 100;
                    }
                }
                
                weightedItems.push({
                    id: buffId,
                    w: weight
                });
            }
        }

        buffIds.length = 0;
        
        for (let i = weightedItems.length - 1; i >= 0; i--) {
            const selectedItem = GameUtil.weightGetValue(weightedItems);
            const index = weightedItems.indexOf(selectedItem);
            buffIds.push(selectedItem.id);
            weightedItems.splice(index, 1);
            
            if (buffIds.length >= maxCount) {
                return buffIds;
            }
        }

        return buffIds;
    }

    excludeBuff(currentBuffs: number[]): void {
        if (!this.isfivecheck) {
            this.curbufflists.forEach((buffId) => {
                if (currentBuffs.indexOf(buffId) === -1) {
                    this.historyBuffs.push(buffId);
                }
            });
        }
    }
}
