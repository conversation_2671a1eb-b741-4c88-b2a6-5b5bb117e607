import { CallID } from "./CallID";
import { CurrencyConfigCfg } from "./CurrencyConfigCfg";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { NotifyID } from "./NotifyID";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { AlertManager } from "./AlertManager";
import { default as ModeBackpackHeroModel } from "./ModeBackpackHeroModel";
import { default as ADModel } from "./ADModel";

enum VideoType {
    Video = 1,
    ADcoupons_in = CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_in,
    ADcoupons_out = CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out
}

export class ADController extends MVC.MController {
    private videoType: VideoType;

    constructor() {
        super();
        this.setup(ADModel.instance);
        this.changeListener(true);
    }

    reset(): void {
        this._model.reset();
    }

    get classname(): string {
        return "ADController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.Ad_ShowVideo, this.onAd_ShowVideo, this);
        Notifier.changeCall(enable, CallID.Ad_VideoType, this.getVideoType, this);
        Notifier.changeCall(enable, CallID.Ad_ModeVideoNum, this.getModeVideoNum, this);
        Notifier.changeListener(enable, ListenID.Game_Load, this.onOpenGame, this, 200);
    }

    get curMode(): number {
        return Notifier.call(CallID.Fight_GetCutMode);
    }

    onOpenGame(): void {
        if (this.curMode > 0) {
            this.mode.data.set(this.curMode, 0);
        }
    }

    getModeVideoNum(): number {
        return this.mode.data.getor(this.curMode, 0);
    }

    getVideoType(): VideoType {
        return this.videoType;
    }

    onAd_ShowVideo(callback: (result: any) => void): void {
        this.mode.isVideoIng = true;
        
        if (Manager.vo.knapsackVo.has(CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out)) {
            this.videoType = VideoType.ADcoupons_out;
            
            if (Manager.vo.userVo.checkIgnorePop(GameSeting.PopViewType.ADCouponsConfig)) {
                Manager.vo.knapsackVo.useUp(CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out);
                ModeBackpackHeroModel.instance.addAdCount();
                callback(wonderSdk.VideoAdCode.COMPLETE);
            } else {
                AlertManager.showSelectAlert({
                    title: "提示",
                    desc: "是否使用广告券跳过广告?",
                    confirmText: "确认",
                    icon: "img/lobby/icon_adcard",
                    viewType: GameSeting.PopViewType.ADCouponsConfig,
                    hasIgnore: true,
                    confirm: () => {
                        Manager.vo.knapsackVo.useUp(CurrencyConfigCfg.CurrencyConfigDefine.adcoupons_out);
                        ModeBackpackHeroModel.instance.addAdCount();
                        callback(wonderSdk.VideoAdCode.COMPLETE);
                    }
                });
            }
            this.mode.isVideoIng = false;
        } else {
            this.videoType = VideoType.Video;
            Notifier.send(NotifyID.Game_LoadingView, true);
            
            wonderSdk.showVideoAD((result: any) => {
                this.mode.isVideoIng = false;
                Notifier.send(NotifyID.Game_LoadingView, false);
                callback(result);
                Notifier.send(ListenID.Task_UpdateProgress, 8);
                this.mode.data.add(this.curMode, 1);
            });
        }
    }
}
