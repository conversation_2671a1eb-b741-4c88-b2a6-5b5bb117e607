// ItemModel.ts
// 从 ItemModel.js 转换而来

import { GameSeting } from "./GameSeting";
import { Cfg } from "./Cfg";
import { CurrencyConfigCfg } from "./CurrencyConfigCfg";
import { MVC } from "./MVC";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { GameUtil } from "./GameUtil";
import GoodsUIItem from "./GoodsUIItem";

/**
 * 道具数据模型
 */
export default class ItemModel extends MVC.Model {
    private static _instance: ItemModel = null;
    
    /** 随机碎片类型 */
    public randomFragment: string[] = [
        CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments,
        CurrencyConfigCfg.CurrencyConfigDefine.BEquipfragments,
        CurrencyConfigCfg.CurrencyConfigDefine.AEquipfragments,
        CurrencyConfigCfg.CurrencyConfigDefine.SEquipfragments
    ];
    
    constructor() {
        super();
        if (!ItemModel._instance) {
            ItemModel._instance = this;
        }
    }
    
    /**
     * 获取单例实例
     */
    public static get instance(): ItemModel {
        if (!ItemModel._instance) {
            ItemModel._instance = new ItemModel();
        }
        return ItemModel._instance;
    }
    
    /**
     * 重置数据
     */
    reset(): void {
        // 重置道具数据
    }
    
    /**
     * 显示道具提示
     * @param options 显示选项
     */
    showItemTips(options: {
        itemID: number;
        nodeAttr?: any;
        timeScale?: number;
        imgNum?: number;
        targetPos?: cc.Vec2;
    }): void {
        const currencyConfig = Cfg.CurrencyConfig.get(options.itemID);
        
        // 设置默认值
        options.nodeAttr = options.nodeAttr || {};
        options.timeScale = options.timeScale || 1;
        options.imgNum = options.imgNum || 5;
        options.targetPos = options.targetPos || 
            GoodsUIItem.getIconPos(currencyConfig.id) || 
            cc.v2(0.6 * GameUtil.getDesignSize.width, GameUtil.getDesignSize.height);
        
        // 加载图标并显示动画
        Manager.loader.loadSpriteImg(currencyConfig.icon).then((sprite: cc.Node) => {
            const distance = cc.Vec2.distance(options.targetPos, options.nodeAttr.position) / 3000;
            
            for (let i = 0; i < options.imgNum; i++) {
                this.createItemIcon(sprite, options, i === 0);
            }
        });
    }
    
    /**
     * 创建道具图标
     * @param sprite 精灵节点
     * @param options 选项
     * @param isOriginal 是否为原始节点
     */
    private createItemIcon(sprite: cc.Node, options: any, isOriginal: boolean): void {
        const iconNode = isOriginal ? sprite : cc.instantiate(sprite);
        
        // 设置节点属性
        iconNode.setAttribute({
            parent: UIManager.layerRoots(MVC.eUILayer.Guide),
            group: "UI",
            ...options.nodeAttr,
            scale: 0
        });
        
        // 设置随机位置偏移
        const randomPos = GameUtil.AngleAndLenToPos(GameUtil.random(0, 360), 50);
        iconNode.setPosition(iconNode.position.add(randomPos));
        
        // 播放缩放和移动动画
        const targetScale = options.nodeAttr.scale || 1;
        cc.tween(iconNode)
            .stopLast()
            .by(0.3 * options.timeScale, { scale: targetScale })
            .delay(0.2 * options.timeScale)
            .to(0.5 * options.timeScale, { position: options.targetPos })
            .call(() => {
                iconNode.destroy();
            })
            .start();
    }
    
    /**
     * 检查是否拥有道具
     * @param itemId 道具ID
     * @param count 数量
     * @returns 是否拥有足够数量
     */
    hasItem(itemId: number, count: number): boolean {
        const currentCount = this.getItemCount(itemId);
        return currentCount >= count;
    }
    
    /**
     * 获取道具数量
     * @param itemId 道具ID
     * @returns 道具数量
     */
    getItemCount(itemId: number): number {
        // 从存储中获取道具数量
        return Manager.storage.getItem(`item_${itemId}`, 0);
    }
    
    /**
     * 添加道具
     * @param itemId 道具ID
     * @param count 数量
     */
    addItem(itemId: number, count: number): void {
        const currentCount = this.getItemCount(itemId);
        const newCount = currentCount + count;
        Manager.storage.setItem(`item_${itemId}`, newCount);
        this.notifyItemChange(itemId, newCount);
    }
    
    /**
     * 使用道具
     * @param itemId 道具ID
     * @param count 数量
     */
    useItem(itemId: number, count: number): void {
        const currentCount = this.getItemCount(itemId);
        if (currentCount >= count) {
            const newCount = currentCount - count;
            Manager.storage.setItem(`item_${itemId}`, newCount);
            this.notifyItemChange(itemId, newCount);
        }
    }
    
    /**
     * 添加货币
     * @param currencyType 货币类型
     * @param amount 数量
     */
    addCurrency(currencyType: string, amount: number): void {
        const currentAmount = this.getCurrencyAmount(currencyType);
        const newAmount = currentAmount + amount;
        Manager.storage.setItem(`currency_${currencyType}`, newAmount);
        this.notifyCurrencyChange(currencyType, newAmount);
    }
    
    /**
     * 获取货币数量
     * @param currencyType 货币类型
     * @returns 货币数量
     */
    getCurrencyAmount(currencyType: string): number {
        return Manager.storage.getItem(`currency_${currencyType}`, 0);
    }
    
    /**
     * 添加装备
     * @param equipId 装备ID
     */
    addEquip(equipId: number): void {
        // 添加装备到背包
        const equipList = this.getEquipList();
        equipList.push(equipId);
        Manager.storage.setItem('equipList', equipList);
        this.notifyEquipChange();
    }
    
    /**
     * 获取装备列表
     * @returns 装备列表
     */
    getEquipList(): number[] {
        return Manager.storage.getItem('equipList', []);
    }
    
    /**
     * 通知道具变化
     * @param itemId 道具ID
     * @param newCount 新数量
     */
    private notifyItemChange(itemId: number, newCount: number): void {
        this.sendNotification('ITEM_COUNT_CHANGE', { itemId, count: newCount });
    }
    
    /**
     * 通知货币变化
     * @param currencyType 货币类型
     * @param newAmount 新数量
     */
    private notifyCurrencyChange(currencyType: string, newAmount: number): void {
        this.sendNotification('CURRENCY_CHANGE', { type: currencyType, amount: newAmount });
    }
    
    /**
     * 通知装备变化
     */
    private notifyEquipChange(): void {
        this.sendNotification('EQUIP_LIST_CHANGE');
    }
}
