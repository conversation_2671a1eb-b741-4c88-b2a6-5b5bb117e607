import { Log } from "./Log";
import { default as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./<PERSON>sKeeper";

export class LoadResArgs {
    url?: string;
    urls?: string[];
    type?: typeof cc.Asset;
    bundle?: cc.AssetManager.Bundle;
    onProgess?: (finished: number, total: number, item: cc.AssetManager.RequestItem) => void;
    onCompleted?: (error: Error, resource: any) => void;
    use?: any;
}

const isChildClassOf = cc.js.isChildClassOf || cc.isChildClassOf;

export default class AssetLoader {
    private _resKeeper: ResKeeper = null;

    static makeLoadResArgs(...args: any[]): LoadResArgs {
        if (args.length < 1) {
            Log.error("_makeLoadResArgs error " + args);
            return null;
        }

        if (args.length === 1 && args[0] instanceof LoadResArgs) {
            return args[0];
        }

        const loadArgs: LoadResArgs = {};

        if (typeof args[0] === "string") {
            loadArgs.url = args[0];
        } else if (args[0] instanceof Array) {
            loadArgs.urls = args[0];
        } else {
            Log.error("_makeLoadResArgs error " + args);
            return null;
        }

        for (let i = 1; i < args.length; ++i) {
            if (i === 1 && isChildClassOf(args[i], cc.Asset)) {
                loadArgs.type = args[i];
            } else if (i === args.length - 1 && args[i] instanceof cc.AssetManager.Bundle) {
                loadArgs.bundle = args[i];
            } else if (typeof args[i] === "function") {
                if (args.length > i + 1 && typeof args[i + 1] === "function") {
                    loadArgs.onProgess = args[i];
                } else {
                    loadArgs.onCompleted = args[i];
                }
            }
        }

        loadArgs.bundle = loadArgs.bundle || cc.resources;
        return loadArgs;
    }

    private _finishItem(url?: string, type?: typeof cc.Asset, use?: any, resKeeper?: ResKeeper): void {
        // Implementation for finishing item loading
    }

    loadRes(...args: any[]): void {
        const loadArgs = AssetLoader.makeLoadResArgs.apply(this, args);
        const resKeeper = this._resKeeper;

        const onCompleted = (error: Error, resource: any) => {
            if (!error) {
                this._finishItem(loadArgs.url, loadArgs.type, loadArgs.use, resKeeper);
            }
            loadArgs.onCompleted?.(error, resource);
        };

        const cachedAsset = loadArgs.bundle.get(loadArgs.url, loadArgs.type);
        if (cachedAsset) {
            onCompleted(null, cachedAsset);
        } else if (loadArgs.url.startsWith("https:")) {
            cc.assetManager.loadRemote(loadArgs.url, onCompleted);
        } else {
            loadArgs.bundle.load(loadArgs.url, loadArgs.type, loadArgs.onProgess, onCompleted);
        }
    }

    preloadRes(...args: any[]): void {
        const loadArgs = AssetLoader.makeLoadResArgs.apply(this, args);
        const resKeeper = this._resKeeper;

        const onCompleted = (error: Error, resource: any) => {
            if (!error) {
                this._finishItem(loadArgs.url, loadArgs.type, loadArgs.use, resKeeper);
            }
            loadArgs.onCompleted?.(error, resource);
        };

        const cachedAsset = loadArgs.bundle.get(loadArgs.url, loadArgs.type);
        if (cachedAsset) {
            onCompleted(null, cachedAsset);
        } else {
            loadArgs.bundle.preload(loadArgs.url, loadArgs.type, loadArgs.onProgess, onCompleted);
        }
    }

    preloadResDir(...args: any[]): void {
        const loadArgs = AssetLoader.makeLoadResArgs.apply(this, args);
        const resKeeper = this._resKeeper;

        const onCompleted = (error: Error, resources: any[]) => {
            if (!error) {
                const dirAssets = loadArgs.bundle.getDirWithPath(loadArgs.url, loadArgs.type);
                dirAssets?.forEach((asset: any) => {
                    this._finishItem(asset.path, loadArgs.type, loadArgs.use, resKeeper);
                });
            }
            loadArgs.onCompleted?.(error, resources);
        };

        loadArgs.bundle.preloadDir(loadArgs.url, loadArgs.type, loadArgs.onProgess, onCompleted);
    }

    loadArray(...args: any[]): void {
        const loadArgs = AssetLoader.makeLoadResArgs.apply(this, args);
        const resKeeper = this._resKeeper;

        const onCompleted = (error: Error, resources: any[]) => {
            if (!error) {
                for (let i = 0; i < loadArgs.urls.length; ++i) {
                    this._finishItem(loadArgs.urls[i], loadArgs.type, loadArgs.use, resKeeper);
                }
            }
            loadArgs.onCompleted?.(error, resources);
        };

        loadArgs.bundle.load(loadArgs.urls, loadArgs.type, loadArgs.onProgess, onCompleted);
    }

    loadResDir(...args: any[]): void {
        const loadArgs = AssetLoader.makeLoadResArgs.apply(this, args);
        const resKeeper = this._resKeeper;

        const onCompleted = (error: Error, resources: any[]) => {
            if (!error) {
                const dirAssets = loadArgs.bundle.getDirWithPath(loadArgs.url, loadArgs.type);
                dirAssets?.forEach((asset: any) => {
                    this._finishItem(asset.path, loadArgs.type, loadArgs.use, resKeeper);
                });
            }
            loadArgs.onCompleted?.(error, resources);
        };

        loadArgs.bundle.loadDir(loadArgs.url, loadArgs.type, loadArgs.onProgess, onCompleted);
    }

    releaseAsset(asset: cc.Asset): void {
        if (asset) {
            cc.assetManager.releaseAsset(asset);
        }
    }

    getResKeeper(): ResKeeper {
        if (!this._resKeeper) {
            this._resKeeper = new ResKeeper();
        }
        return this._resKeeper;
    }
}

export const assetLoader = new AssetLoader();
