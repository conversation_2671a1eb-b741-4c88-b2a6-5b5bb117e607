// GuidesModel.ts
// 从 GuidesModel.js 转换而来

import { Cfg } from "./Cfg";
import { MVC } from "./MVC";

/**
 * 引导数据模型
 */
export default class GuidesModel extends MVC.Model {
    private currentGuideId: number = 0;
    private currentStep: number = 0;
    private isGuiding: boolean = false;

    constructor() {
        super();
    }

    /**
     * 开始引导
     * @param guideId 引导ID
     */
    startGuide(guideId: number): void {
        this.currentGuideId = guideId;
        this.currentStep = 0;
        this.isGuiding = true;
    }

    /**
     * 下一步
     */
    nextStep(): void {
        if (this.isGuiding) {
            this.currentStep++;
        }
    }

    /**
     * 完成引导
     */
    completeGuide(): void {
        this.isGuiding = false;
        this.currentGuideId = 0;
        this.currentStep = 0;
    }

    /**
     * 获取当前引导ID
     */
    getCurrentGuideId(): number {
        return this.currentGuideId;
    }

    /**
     * 获取当前步骤
     */
    getCurrentStep(): number {
        return this.currentStep;
    }

    /**
     * 是否正在引导
     */
    isInGuide(): boolean {
        return this.isGuiding;
    }

    /**
     * 获取引导配置
     */
    getGuideConfig(): any {
        return Cfg.GuideCfg.get(this.currentGuideId);
    }
}