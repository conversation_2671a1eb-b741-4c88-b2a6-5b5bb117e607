var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.LoadingView = undefined;
var $2MVC = require("MVC");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var exp_LoadingView = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._count = 0;
    t.downTime = 10;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.onShow = function () {};
  _ctor.prototype.onHide = function () {};
  _ctor.prototype.changeListener = function () {};
  _ctor.prototype.onOpen = function () {};
  _ctor.prototype.setInfo = function () {
    if (this._openArgs.param) {
      this._count++;
    } else {
      this._count--;
    }
    this._count < 0 && (this._count = 0);
    this.setActive(this._count > 0);
    this._count > 0 && (this.downTime = 2);
  };
  _ctor.prototype.onClose = function () {};
  _ctor.prototype.onShowFinish = function () {
    this.offTouch();
  };
  _ctor.prototype.onHideFinish = function () {};
  _ctor.prototype.update = function (e) {
    if (this._count > 0 && this.downTime >= 0) {
      this.downTime -= e;
      if (this.downTime <= 0) {
        this._count = 0, this.setActive(this._count > 0);
      }
    }
  };
  _ctor.prototype.setActive = function (e) {
    e != this.node.children[0].active && (this.node.children[0].active = e);
  };
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/Loading/LoadingView"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Loading)], _ctor);
}($2MVC.MVC.BaseView);
exports.LoadingView = exp_LoadingView;