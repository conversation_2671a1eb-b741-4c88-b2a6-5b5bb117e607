import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Cfg } from "./Cfg";
import { GameatrCfg } from "./GameatrCfg";
import { Notifier } from "./Notifier";
import { GameUtil } from "./GameUtil";
import { Game, ModeCfg } from "./Game";
import { BaseEntity } from "./BaseEntity";

export namespace Buff {
    const buffCache: { [key: string]: any } = {};
    export const MoveAtr = [GameatrCfg.GameatrDefine.movespeed, GameatrCfg.GameatrDefine.freeze];
    
    let freezeIdList: number[];
    const buffEventList = ["Buff_EntityDead", "Buff_Hurt", "Buff_ResistDamage", "Buff_SetSlash"];

    export function FreezeIDList(): number[] {
        if (!freezeIdList) {
            freezeIdList = [];
            ModeCfg.Buff.forEach((buff: any) => {
                if (buff.type === 2 && buff.attr.includes(GameatrCfg.GameatrDefine.freeze)) {
                    freezeIdList.push(buff.id);
                }
            });
        }
        return freezeIdList;
    }

    export function cacheBuff(buffId: string): () => void {
        return () => {
            const rf = (cc as any)._RF.peek();
            const script = rf && rf.script;
            buffCache[buffId] = script;
        };
    }

    export class BuffItem {
        public attrMap = new GameSeting.TMap<number, number>();
        public specialMap: any[] = [];
        private _curBuffTime: number = 0;
        public isForever: boolean = false;
        private _isActive: boolean = true;
        public owner: BaseEntity = null;
        public caster: BaseEntity = null;
        private _buffLayer: number = 0;
        public tempData: any = {};
        public isEnemyAddDebuff: boolean = false;
        public cutEffect: any[] = [];
        public cutEffectPath: string[] = [];
        
        public ID: number;
        private _buffCfg: any;
        private _cutVo: any;
        public isSpecificSkill: boolean;

        constructor() {
            this.attrMap = new GameSeting.TMap<number, number>();
            this.specialMap = [];
            this._curBuffTime = 0;
            this.isForever = false;
            this._isActive = true;
            this.owner = null;
            this.caster = null;
            this._buffLayer = 0;
            this.tempData = {};
            this.isEnemyAddDebuff = false;
            this.cutEffect = [];
            this.cutEffectPath = [];
        }

        get buffCfg(): any {
            return this._cutVo;
        }

        set buffCfg(config: any) {
            this.ID = config.id;
            this._buffCfg = { ...config };
            this._buffCfg.weight = typeof this._buffCfg.weight === "number" ? this._buffCfg.weight : 1;
            this._buffCfg.isOverlay = this._buffCfg.isOverlay || 1;
            this._cutVo = JSON.parse(JSON.stringify(this._buffCfg));
            this.checkGain();
            this.curBuffTime = this.isForever ? 999 : this._cutVo.time;
        }

        get cutVo(): any {
            return this._cutVo;
        }

        set cutVo(value: any) {
            this._cutVo = value;
        }

        get otherValue(): any {
            return this.cutVo.otherValue;
        }

        get relatedBuffAttr(): GameSeting.TMap<number, number> {
            // Implementation for getting related buff attributes
            return new GameSeting.TMap<number, number>();
        }

        checkGain(cutVo = this._cutVo, buffCfg = this._buffCfg, relatedAttr = this.relatedBuffAttr): void {
            cutVo.weight = buffCfg.weight + relatedAttr.getor(GameatrCfg.GameatrDefine.buffWeight, 0);
            cutVo.isOverlay = buffCfg.isOverlay + relatedAttr.getor(GameatrCfg.GameatrDefine.buffOverlay, 0);
            
            const otherValueAdd = relatedAttr.getor(GameatrCfg.GameatrDefine.otherValueAdd, 0);
            buffCfg.otherValue?.forEach((value: number, index: number) => {
                cutVo.otherValue[index] = value * (1 + otherValueAdd);
            });

            cutVo.value.forEach((value: number[], index: number) => {
                if (buffCfg.attr[index][0] !== -1) {
                    value[0] = buffCfg.value[index][0] * (1 + relatedAttr.getor(GameatrCfg.GameatrDefine.buffEffectAdd, 0));
                }
            });

            if (buffCfg.time !== -1) {
                cutVo.time = buffCfg.time + relatedAttr.getor(GameatrCfg.GameatrDefine.buffDur, 0);
            }

            this.isSpecificSkill = cutVo.skillId;
            if (!this.isForever) {
                this.isForever = cutVo.time === -1;
            }
        }

        get curBuffTime(): number {
            return this._curBuffTime;
        }

        set curBuffTime(value: number) {
            this._curBuffTime = value;
        }

        get isActive(): boolean {
            return this._isActive;
        }

        set isActive(value: boolean) {
            this._isActive = value;
        }

        get buffLayer(): number {
            return this._buffLayer;
        }

        set buffLayer(value: number) {
            this._buffLayer = value;
        }

        // Additional methods would be added here based on the original implementation
        init(): void {
            // Initialization logic
        }

        update(deltaTime: number): void {
            if (!this.isForever && this._curBuffTime > 0) {
                this._curBuffTime -= deltaTime;
                if (this._curBuffTime <= 0) {
                    this.onBuffEnd();
                }
            }
        }

        onBuffStart(): void {
            // Buff start logic
        }

        onBuffEnd(): void {
            // Buff end logic
        }

        destroy(): void {
            // Cleanup logic
        }
    }
}
