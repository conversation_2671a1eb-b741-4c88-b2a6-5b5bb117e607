// LoadingModel.ts
// 从 LoadingModel.js 转换而来

import { MVC } from "./MVC";

/**
 * 加载数据模型
 */
export default class LoadingModel extends MVC.Model {
    /** 是否正在加载 */
    private isLoading: boolean = false;
    
    /** 加载进度 (0-100) */
    private progress: number = 0;
    
    /** 加载文本 */
    private loadingText: string = "加载中...";
    
    /** 是否显示进度条 */
    private showProgress: boolean = true;
    
    /** 是否可以取消 */
    private canCancel: boolean = false;
    
    /** 加载开始时间 */
    private startTime: number = 0;
    
    /** 预计完成时间 */
    private estimatedTime: number = 0;
    
    constructor() {
        super();
    }
    
    /**
     * 设置加载状态
     * @param loading 是否正在加载
     */
    setLoadingState(loading: boolean): void {
        this.isLoading = loading;
        
        if (loading) {
            this.startTime = Date.now();
            this.progress = 0;
        }
        
        this.notifyChange();
    }
    
    /**
     * 获取加载状态
     */
    getLoadingState(): boolean {
        return this.isLoading;
    }
    
    /**
     * 设置加载进度
     * @param progress 进度值 (0-100)
     */
    setProgress(progress: number): void {
        this.progress = Math.max(0, Math.min(100, progress));
        
        // 计算预计完成时间
        if (this.progress > 0 && this.startTime > 0) {
            const elapsed = Date.now() - this.startTime;
            this.estimatedTime = (elapsed / this.progress) * (100 - this.progress);
        }
        
        this.notifyChange();
    }
    
    /**
     * 获取加载进度
     */
    getProgress(): number {
        return this.progress;
    }
    
    /**
     * 设置加载文本
     * @param text 加载文本
     */
    setLoadingText(text: string): void {
        this.loadingText = text;
        this.notifyChange();
    }
    
    /**
     * 获取加载文本
     */
    getLoadingText(): string {
        return this.loadingText;
    }
    
    /**
     * 设置是否显示进度条
     * @param show 是否显示
     */
    setShowProgress(show: boolean): void {
        this.showProgress = show;
        this.notifyChange();
    }
    
    /**
     * 获取是否显示进度条
     */
    getShowProgress(): boolean {
        return this.showProgress;
    }
    
    /**
     * 设置是否可以取消
     * @param canCancel 是否可以取消
     */
    setCanCancel(canCancel: boolean): void {
        this.canCancel = canCancel;
        this.notifyChange();
    }
    
    /**
     * 获取是否可以取消
     */
    getCanCancel(): boolean {
        return this.canCancel;
    }
    
    /**
     * 获取已用时间（毫秒）
     */
    getElapsedTime(): number {
        if (this.startTime === 0) return 0;
        return Date.now() - this.startTime;
    }
    
    /**
     * 获取预计剩余时间（毫秒）
     */
    getEstimatedRemainingTime(): number {
        return this.estimatedTime;
    }
    
    /**
     * 获取格式化的时间字符串
     * @param milliseconds 毫秒数
     */
    formatTime(milliseconds: number): string {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        
        if (minutes > 0) {
            return `${minutes}分${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }
    
    /**
     * 获取加载速度（每秒进度）
     */
    getLoadingSpeed(): number {
        const elapsed = this.getElapsedTime();
        if (elapsed === 0) return 0;
        
        return (this.progress / elapsed) * 1000; // 每秒进度
    }
    
    /**
     * 重置加载数据
     */
    reset(): void {
        this.isLoading = false;
        this.progress = 0;
        this.loadingText = "加载中...";
        this.showProgress = true;
        this.canCancel = false;
        this.startTime = 0;
        this.estimatedTime = 0;
        this.notifyChange();
    }
    
    /**
     * 获取完整的加载信息
     */
    getLoadingInfo(): {
        isLoading: boolean;
        progress: number;
        text: string;
        showProgress: boolean;
        canCancel: boolean;
        elapsedTime: number;
        estimatedRemainingTime: number;
        loadingSpeed: number;
    } {
        return {
            isLoading: this.isLoading,
            progress: this.progress,
            text: this.loadingText,
            showProgress: this.showProgress,
            canCancel: this.canCancel,
            elapsedTime: this.getElapsedTime(),
            estimatedRemainingTime: this.getEstimatedRemainingTime(),
            loadingSpeed: this.getLoadingSpeed()
        };
    }
    
    /**
     * 通知数据变化
     */
    private notifyChange(): void {
        this.sendNotification("LOADING_MODEL_CHANGED", this.getLoadingInfo());
    }
    
    /**
     * 设置加载配置
     * @param config 配置对象
     */
    setLoadingConfig(config: {
        text?: string;
        showProgress?: boolean;
        canCancel?: boolean;
    }): void {
        if (config.text !== undefined) {
            this.setLoadingText(config.text);
        }
        
        if (config.showProgress !== undefined) {
            this.setShowProgress(config.showProgress);
        }
        
        if (config.canCancel !== undefined) {
            this.setCanCancel(config.canCancel);
        }
    }
    
    /**
     * 增加进度
     * @param increment 增加的进度值
     */
    addProgress(increment: number): void {
        this.setProgress(this.progress + increment);
    }
    
    /**
     * 检查是否加载完成
     */
    isComplete(): boolean {
        return this.progress >= 100;
    }
    
    /**
     * 获取进度百分比字符串
     */
    getProgressText(): string {
        return `${Math.round(this.progress)}%`;
    }
}
