import { MVC } from "./MVC";
import { Game } from "./Game";
import { default as BuffModel } from "./BuffModel";

export class BuffController extends MVC.MController {
    constructor() {
        super();
        this.setup(BuffModel.instance);
        this.changeListener(true);
    }

    get mode(): BuffModel {
        return BuffModel.instance;
    }

    reset(): void {}

    get classname(): string {
        return "BuffController";
    }

    registerAllProtocol(): void {}

    get game(): any {
        return Game.Mgr.instance;
    }

    changeListener(enable?: boolean): void {
        // Listener implementation
    }
}
