var i;
var cc__extends = __extends;
var cc__assign = __assign;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2GameSeting = require("GameSeting");
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Manager = require("Manager");
var $2UIManager = require("UIManager");
var $2GameUtil = require("GameUtil");
var $2GoodsUIItem = require("GoodsUIItem");
var def_ItemModel = function (e) {
  function _ctor() {
    var o = e.call(this) || this;
    o.randomFragment = [$2CurrencyConfigCfg.CurrencyConfigDefine.Equipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.BEquipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.AEquipfragments, $2CurrencyConfigCfg.CurrencyConfigDefine.SEquipfragments];
    null == _ctor._instance && (_ctor._instance = o);
    return o;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {};
  Object.defineProperty(_ctor, "instance", {
    get: function () {
      null == _ctor._instance && (_ctor._instance = new _ctor());
      return _ctor._instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.showItemTips = function (e) {
    var t = $2Cfg.Cfg.CurrencyConfig.get(e.itemID);
    e.nodeAttr || (e.nodeAttr = {});
    e.timeScale || (e.timeScale = 1);
    e.imgNum || (e.imgNum = 5);
    e.targetPos || (e.targetPos = $2GoodsUIItem.default.getIconPos(t.id) || cc.v2(.6 * $2GameUtil.GameUtil.getDesignSize.width, $2GameUtil.GameUtil.getDesignSize.height));
    $2Manager.Manager.loader.loadSpriteImg(t.icon).then(function (t) {
      var o = cc.Vec2.distance(e.targetPos, e.nodeAttr.position) / 3e3;
      var i = function (i) {
        var n = 0 == i ? t : cc.instantiate(t);
        n.setAttribute(cc__assign(cc__assign({
          parent: $2UIManager.UIManager.layerRoots($2MVC.MVC.eUILayer.Guide),
          group: "UI"
        }, e.nodeAttr), {
          scale: 0
        }));
        n.setPosition(n.position.add($2GameUtil.GameUtil.AngleAndLenToPos($2GameUtil.GameUtil.random(0, 360), 50)));
        cc.tween(n).stopLast().by(.3 * e.timeScale, {
          scale: e.nodeAttr.scale || 1,
          y: 100
        }, {
          easing: cc.easing.backOut
        }).to(o * e.timeScale, {
          position: e.targetPos
        }).to(.1, {
          scale: 0
        }).call(function () {
          0 == i && e.call && e.call();
          n.destroy();
        }).start();
      };
      for (var n = 0; n < e.imgNum; n++) {
        i(n);
      }
    });
  };
  _ctor.prototype.arrToReward = function (e, t) {
    undefined === t && (t = 2);
    var o = [];
    for (var i = 0; i < e.length; i += t) {
      var n = $2Cfg.Cfg.CurrencyConfig.get(e[i]);
      var r = e[i + 1];
      o.push({
        id: n.id,
        num: r,
        type: n.type,
        rarity: n.rarity
      });
    }
    return o;
  };
  _ctor.prototype.briefRewardArrTo = function (e) {
    var t = this;
    var o = [];
    e.forEach(function (e) {
      var i = e[0];
      var n = e[1];
      var r = e[2] || 0;
      var c = $2Cfg.Cfg.CurrencyConfig.get(i);
      var l = $2Cfg.Cfg.RoleUnlock.getArray().filter(function (e) {
        return 1 == e.type;
      });
      if (!c) {
        return;
      }
      if (c.type == $2GameSeting.GameSeting.GoodsType.Fragment) {
        if (t.randomFragment.includes(i)) {
          var u = l.filter(function (e) {
            return e.rarity == c.rarity;
          });
          var p = function () {
            var e = $2GameUtil.GameUtil.getRandomInArray(u)[0].id;
            var t = o.find(function (t) {
              return t.id == e;
            });
            if (t) {
              t.num += 1;
            } else {
              o.push({
                id: e,
                num: 1,
                type: c.type,
                rarity: c.rarity,
                param: r
              });
            }
          };
          for (var h = 0; h < n; h++) {
            p();
          }
        } else {
          o.push({
            id: c.id,
            num: n,
            type: c.type,
            rarity: c.rarity,
            param: r
          });
        }
      } else {
        o.push({
          id: c.id,
          num: n,
          type: c.type,
          rarity: +c.rarity,
          param: r
        });
      }
    });
    return o;
  };
  _ctor.prototype.briefRewardTo = function (e) {
    var t = this;
    var o = [];
    e.forEach(function (e) {
      o.push.apply(o, t.briefRewardArrTo([[e.id, e.num]]));
    });
    return o;
  };
  _ctor._instance = null;
  return _ctor;
}($2MVC.MVC.BaseModel);
exports.default = def_ItemModel;