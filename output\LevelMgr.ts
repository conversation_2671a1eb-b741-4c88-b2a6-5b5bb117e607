// LevelMgr.ts
// 从 LevelMgr.js 转换而来

import { Cfg } from "./Cfg";
import { Manager } from "./Manager";
import { ItemModel } from "./ItemModel";
import { ModeBackpackHeroModel } from "./ModeBackpackHeroModel";
import { RecordVo } from "./RecordVo";

/**
 * 等级管理器
 */
export class Level {
    private static _instance: Level = null;
    
    /** 当前关卡ID */
    private currentLevelId: number = 1;
    
    /** 最高通关关卡 */
    private maxPassedLevel: number = 0;
    
    /** 关卡进度数据 */
    private levelProgress: Map<number, any> = new Map();
    
    constructor() {
        if (!Level._instance) {
            Level._instance = this;
        }
        this.loadLevelData();
    }
    
    /**
     * 获取单例实例
     */
    public static get instance(): Level {
        if (!Level._instance) {
            Level._instance = new Level();
        }
        return Level._instance;
    }
    
    /**
     * 加载关卡数据
     */
    private loadLevelData(): void {
        this.currentLevelId = Manager.storage.getItem("currentLevelId", 1);
        this.maxPassedLevel = Manager.storage.getItem("maxPassedLevel", 0);
        
        const progressData = Manager.storage.getItem("levelProgress", "{}");
        this.levelProgress = new Map(Object.entries(JSON.parse(progressData)));
    }
    
    /**
     * 保存关卡数据
     */
    private saveLevelData(): void {
        Manager.storage.setItem("currentLevelId", this.currentLevelId);
        Manager.storage.setItem("maxPassedLevel", this.maxPassedLevel);
        
        const progressObj = Object.fromEntries(this.levelProgress);
        Manager.storage.setItem("levelProgress", JSON.stringify(progressObj));
    }
    
    /**
     * 获取当前关卡ID
     */
    getCurrentLevelId(): number {
        return this.currentLevelId;
    }
    
    /**
     * 设置当前关卡
     * @param levelId 关卡ID
     */
    setCurrentLevel(levelId: number): void {
        this.currentLevelId = levelId;
        this.saveLevelData();
    }
    
    /**
     * 获取最高通关关卡
     */
    getMaxPassedLevel(): number {
        return this.maxPassedLevel;
    }
    
    /**
     * 获取关卡配置
     * @param levelId 关卡ID
     */
    getLevelConfig(levelId: number): any {
        return Cfg.LevelCfg.get(levelId);
    }
    
    /**
     * 检查关卡是否解锁
     * @param levelId 关卡ID
     */
    isLevelUnlocked(levelId: number): boolean {
        return levelId <= this.maxPassedLevel + 1;
    }
    
    /**
     * 开始关卡
     * @param levelId 关卡ID
     */
    startLevel(levelId: number): boolean {
        if (!this.isLevelUnlocked(levelId)) {
            console.warn(`关卡 ${levelId} 未解锁`);
            return false;
        }
        
        const levelConfig = this.getLevelConfig(levelId);
        if (!levelConfig) {
            console.error(`关卡配置不存在: ${levelId}`);
            return false;
        }
        
        this.setCurrentLevel(levelId);
        
        // 记录关卡开始
        this.recordLevelStart(levelId);
        
        return true;
    }
    
    /**
     * 完成关卡
     * @param levelId 关卡ID
     * @param score 得分
     * @param stars 星级
     */
    completeLevel(levelId: number, score: number = 0, stars: number = 1): void {
        // 更新最高通关关卡
        if (levelId > this.maxPassedLevel) {
            this.maxPassedLevel = levelId;
        }
        
        // 更新关卡进度
        const progress = this.levelProgress.get(levelId) || {};
        progress.completed = true;
        progress.bestScore = Math.max(progress.bestScore || 0, score);
        progress.bestStars = Math.max(progress.bestStars || 0, stars);
        progress.completedTimes = (progress.completedTimes || 0) + 1;
        progress.lastCompletedTime = Date.now();
        
        this.levelProgress.set(levelId, progress);
        
        // 发放奖励
        this.giveRewards(levelId, stars);
        
        // 记录关卡完成
        this.recordLevelComplete(levelId, score, stars);
        
        this.saveLevelData();
    }
    
    /**
     * 关卡失败
     * @param levelId 关卡ID
     */
    failLevel(levelId: number): void {
        const progress = this.levelProgress.get(levelId) || {};
        progress.failedTimes = (progress.failedTimes || 0) + 1;
        progress.lastFailedTime = Date.now();
        
        this.levelProgress.set(levelId, progress);
        
        // 记录关卡失败
        this.recordLevelFail(levelId);
        
        this.saveLevelData();
    }
    
    /**
     * 获取关卡进度
     * @param levelId 关卡ID
     */
    getLevelProgress(levelId: number): any {
        return this.levelProgress.get(levelId) || {
            completed: false,
            bestScore: 0,
            bestStars: 0,
            completedTimes: 0,
            failedTimes: 0
        };
    }
    
    /**
     * 发放关卡奖励
     * @param levelId 关卡ID
     * @param stars 星级
     */
    private giveRewards(levelId: number, stars: number): void {
        const levelConfig = this.getLevelConfig(levelId);
        if (!levelConfig || !levelConfig.rewards) return;
        
        const itemModel = Manager.getModel(ItemModel);
        const heroModel = Manager.getModel(ModeBackpackHeroModel);
        
        // 根据星级发放不同奖励
        const rewards = levelConfig.rewards[stars] || levelConfig.rewards[1];
        
        if (rewards) {
            for (const reward of rewards) {
                switch (reward.type) {
                    case "coin":
                        itemModel.addCurrency("coin", reward.amount);
                        break;
                    case "exp":
                        heroModel.addExp(reward.amount);
                        break;
                    case "item":
                        itemModel.addItem(reward.itemId, reward.amount);
                        break;
                }
            }
        }
    }
    
    /**
     * 记录关卡开始
     * @param levelId 关卡ID
     */
    private recordLevelStart(levelId: number): void {
        RecordVo.record("level_start", {
            levelId: levelId,
            timestamp: Date.now()
        });
    }
    
    /**
     * 记录关卡完成
     * @param levelId 关卡ID
     * @param score 得分
     * @param stars 星级
     */
    private recordLevelComplete(levelId: number, score: number, stars: number): void {
        RecordVo.record("level_complete", {
            levelId: levelId,
            score: score,
            stars: stars,
            timestamp: Date.now()
        });
    }
    
    /**
     * 记录关卡失败
     * @param levelId 关卡ID
     */
    private recordLevelFail(levelId: number): void {
        RecordVo.record("level_fail", {
            levelId: levelId,
            timestamp: Date.now()
        });
    }
    
    /**
     * 获取下一个关卡ID
     */
    getNextLevelId(): number {
        return this.currentLevelId + 1;
    }
    
    /**
     * 重置关卡进度
     */
    resetProgress(): void {
        this.currentLevelId = 1;
        this.maxPassedLevel = 0;
        this.levelProgress.clear();
        this.saveLevelData();
    }
    
    /**
     * 获取总通关关卡数
     */
    getTotalCompletedLevels(): number {
        return this.maxPassedLevel;
    }
    
    /**
     * 获取关卡统计信息
     */
    getLevelStats(): {
        totalLevels: number;
        completedLevels: number;
        currentLevel: number;
        completionRate: number;
    } {
        const totalLevels = Object.keys(Cfg.LevelCfg.getAllConfigs()).length;
        const completedLevels = this.maxPassedLevel;
        
        return {
            totalLevels,
            completedLevels,
            currentLevel: this.currentLevelId,
            completionRate: totalLevels > 0 ? completedLevels / totalLevels : 0
        };
    }
}

export { Level };
