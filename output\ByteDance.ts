import { BaseSdk, VideoAdCode } from "./BaseSdk";
import { SdkAudioAdapter } from "./AudioAdapter";
import { GameUtil } from "./GameUtil";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import ttPostbackCtl from "./ttPostbackCtl";
import { CallID } from "./CallID";
import { Manager } from "./Manager";
import { getOpenid } from "./Api";
import { UIManager } from "./UIManager";
import { MVC } from "./MVC";

const tt = (window as any).tt;

export default class ByteDance extends BaseSdk {
    private _bannerAd: any = null;
    private _cdKey: string = "";
    private _showBannerNum: number = 0;
    private _videoAd: any = null;
    public ispreLoadVideo: boolean = false;
    public lastVideoPlayTime: number = 0;
    private _onPlayEnd: Function | null = null;
    public interstitialAd: any = null;
    private _isCanNavigateTo: boolean = false;
    private _isCanAddtoDestop: boolean = true;
    private _curscene: string = "";
    public shareList: any;

    logout(): void { }

    setShareList(shareList: any): void {
        console.log("获取到分享配置", shareList);
        this.shareList = shareList;
    }

    init(config: any): void {
        super.init(config);
        if ((window as any).tt) {
            ttPostbackCtl.GetInstance().init();
            tt.setKeepScreenOn({
                keepScreenOn: true
            });
            tt.showShareMenu({
                success: () => {
                    console.log("已成功显示转发按钮");
                },
                fail: (e: any) => {
                    console.log("showShareMenu 调用失败", e.errMsg);
                },
                complete: () => {
                    console.log("showShareMenu 调用完成");
                }
            });
            tt.onShareAppMessage((e: any) => {
                if (e && "video" == e.channel) {
                    return {
                        channel: "video",
                        title: "最强小兵",
                        imageUrl: "",
                        extra: {
                            withVideoId: true
                        },
                        success: (e: any) => {
                            console.warn("分享成功", e);
                        },
                        fail: (e: any) => {
                            console.warn("分享失败", e);
                        }
                    };
                } else {
                    return {
                        title: "最强小兵",
                        desc: GameUtil.weightGetValue(this.shareList, "weight"),
                        success: () => { },
                        fail: () => { }
                    };
                }
            });
            if (tt.getUpdateManager) {
                const updateManager = tt.getUpdateManager();
                updateManager.onCheckForUpdate((e: any) => {
                    e.hasUpdate && tt.showToast({
                        title: "即将有更新请留意"
                    });
                });
                updateManager.onUpdateReady(() => {
                    tt.showModal({
                        title: "更新提示",
                        content: "新版本已经准备好，是否立即使用？",
                        success: (e: any) => {
                            if (e.confirm) {
                                updateManager.applyUpdate();
                            } else {
                                tt.showToast({
                                    icon: "none",
                                    title: "小程序下一次「冷启动」时会使用新版本"
                                });
                            }
                        }
                    });
                });
                updateManager.onUpdateFailed(() => { });
            }
            Notifier.changeListener(true, ListenID.Platform_NavigateTo, this.navigateToScene, this);
            Notifier.changeListener(true, ListenID.Platform_AddtoDestop, this.addtodestop, this);
            Notifier.changeListener(true, ListenID.Login_Finish, this.onLogin_Finish, this);
            Notifier.changeListener(true, ListenID.ByteDance_Check_Gift, this.checkGiftCode, this);
            Notifier.changeCall(true, CallID.Platform_CanNavigateTo, this.isCanNavigateTo, this);
            Notifier.changeCall(true, CallID.Platform_CanAddDestop, this.isCanAddDestop, this);
            Notifier.changeCall(true, CallID.Platform_GetScene, this.getScene, this);
            Notifier.changeCall(true, CallID.Platform_Query, this.getQuery, this);
            Notifier.changeCall(true, CallID.Platform_CdKey, this.getCdKey, this);
            const launchOptions = tt?.getLaunchOptionsSync();
            console.log("[getLaunchOptionsSync]", launchOptions);
            tt.onShow((e: any) => {
                console.log("进入前台onshow", e);
                this.checkSceneValue(e?.scene);
                this.checkGiftCode(e?.query?.game_cdkey);
            });
        }
    }

    getCdKey(): string | undefined {
        if ("" == this._cdKey || null == this._cdKey || undefined == this._cdKey) {
            const query = this.getQuery();
            if (null === query || undefined === query) {
                return undefined;
            } else {
                return query.game_cdkey;
            }
        } else {
            return this._cdKey;
        }
    }

    checkGiftCode(cdKey: string, isLive?: boolean): void {
        this._cdKey = cdKey;
        console.log("checkGiftCode gameCdKey", this._cdKey);
        if ("" != this._cdKey && null != this._cdKey && undefined != this._cdKey) {
            const storedList = cc.sys.localStorage.getItem("received_gift_code_list", []);
            let receivedList: string[] = [];
            if (storedList && "" != storedList) {
                receivedList = JSON.parse(storedList) || [];
            }
            if (this._cdKey && !receivedList.includes(this._cdKey)) {
                if (isLive) {
                    UIManager.OpenInQueue("ui/setting/ExchangeCodeView", MVC.openArgs().setParam({
                        isLive: true
                    }));
                } else {
                    Notifier.send(ListenID.Activity_OpenExchangeCode, true);
                }
            }
        }
    }

    onLogin_Finish(): void {
        console.log("[ByteDance][onLogin_Finish]");
        if (!tt?.checkScene) {
            return console.log("缺少[checkScene]方法");
        }
        tt?.checkScene({
            scene: "sidebar",
            success: (result: any) => {
                console.log("[checkScene]success", result);
                this._isCanNavigateTo = result?.isExist;
                Notifier.send(ListenID.Main_ResetView);
            },
            complete: (e: any) => {
                console.log("[checkScene]complete", e);
            },
            fail: (e: any) => {
                console.log("[checkScene]fail", e);
            }
        });
        if ("android" == tt.getSystemInfoSync().platform) {
            tt.checkShortcut({
                success: (e: any) => {
                    console.log("检查快捷方式", e.status);
                    if (e.status.exist && !Manager.vo.knapsackVo.has("TTScenc_destop")) {
                        Manager.vo.knapsackVo.addGoods("TTScenc_destop");
                    }
                },
                fail: (e: any) => {
                    console.log("检查快捷方式失败", e.errMsg);
                }
            });
        }
    }

    getQuery(): any {
        const launchOptions = tt.getLaunchOptionsSync();
        if (null === launchOptions || undefined === launchOptions) {
            return undefined;
        } else {
            return launchOptions.query;
        }
    }

    login(successCallback?: Function, failCallback?: Function): Promise<boolean> {
        return new Promise((resolve, reject) => {
            tt.login({
                force: true,
                success: (result: any) => {
                    console.log("## login: ", result);
                    console.log("## app_name: " + (window as any).wonderSdk.BMS_APP_NAME);
                    getOpenid({
                        code: result.code,
                        app_name: (window as any).wonderSdk.BMS_APP_NAME
                    }, (response: any) => {
                        if (0 == response.data.code) {
                            console.log("## 获取openid成功 ：", response, response.data.data.openid);
                            Manager.vo.openId = response.data.data.openid;
                            resolve(true);
                            successCallback && successCallback(null);
                        } else {
                            console.warn("## 获取openid失败，返回参数为：", response.data);
                            reject(false);
                            failCallback && failCallback(null);
                        }
                    }, (error: any) => {
                        console.warn("## 调用获取openid接口失败：", error);
                        reject(false);
                        failCallback && failCallback(null);
                    });
                }
            });
        });
    }

    showBannerWithNode(adUnitId: string, node: any, callback?: Function): void {
        this.showBannerWithStyle(adUnitId, {}, callback);
    }

    realNameAuth(): void {
        if (tt.authenticateRealName) {
            tt.authenticateRealName({
                success: () => {
                    console.log("用户实名认证成功");
                },
                fail: (e: any) => {
                    console.log("用户实名认证失败", e.errMsg);
                }
            });
        }
    }

    showBannerWithStyle(adUnitId: string, style: any, callback?: Function): void {
        if (this._isByteDancePlatform && tt.createBannerAd) {
            const systemInfo = tt.getSystemInfoSync();
            const windowWidth = systemInfo.windowWidth;
            const windowHeight = systemInfo.windowHeight;
            this._showBannerNum++;
            if (this._bannerAd) {
                if (this._showBannerNum <= 1) {
                    this._bannerAd.show().then(() => {
                        console.log("广告展示成功");
                    }).catch((e: any) => {
                        console.error("广告组件出现问题", e);
                        this._showBannerNum--;
                        if (this._showBannerNum < 0) {
                            this._showBannerNum = 0;
                        }
                    });
                }
            } else {
                this._bannerAd = tt.createBannerAd({
                    adUnitId: adUnitId,
                    adIntervals: 30,
                    style: {
                        width: 208,
                        top: windowHeight - 117
                    }
                });
                this._bannerAd.style.left = (windowWidth - 208) / 2;
                this._bannerAd.onResize((e: any) => {
                    this._bannerAd.style.top = windowHeight - e.height;
                    this._bannerAd.style.left = (windowWidth - e.width) / 2;
                });
                this._bannerAd.onLoad(() => {
                    callback && callback();
                    if (this._showBannerNum > 0) {
                        this._bannerAd.show().then(() => {
                            console.log("广告展示成功");
                        }).catch((e: any) => {
                            console.error("广告组件出现问题", e);
                            this._showBannerNum--;
                            if (this._showBannerNum < 0) {
                                this._showBannerNum = 0;
                            }
                        });
                    }
                });
            }
        } else {
            console.log("not support");
        }
    }

    hideBanner(): void {
        this._showBannerNum--;
        if (this._showBannerNum <= 0) {
            this._showBannerNum = 0;
            this._bannerAd && this._bannerAd.hide();
        }
    }

    destroyBanner(): void {
        if (this._bannerAd) {
            this._bannerAd.destroy();
            this._bannerAd = null;
        }
    }

    preLoadRewardVideo(): void { }

    showVideoAD(adUnitId: string, callback?: Function): void {
        this._onPlayEnd = null;
        this._onPlayEnd = callback;
        if ((window as any).wonderSdk.isTest) {
            this._onPlayEnd && this._onPlayEnd(VideoAdCode.COMPLETE, "");
        } else if (this._isByteDancePlatform && tt.createRewardedVideoAd) {
            if (Date.now() - this.lastVideoPlayTime < 1000) {
                this._onPlayEnd && this._onPlayEnd(VideoAdCode.NOT_READY, "视频广告还在准备中，请稍后尝试");
            } else {
                if (!this._videoAd) {
                    this._videoAd = tt.createRewardedVideoAd({
                        adUnitId: adUnitId
                    });
                    this._videoAd.onLoad(() => {
                        // ttPostbackCtl.GetInstance().adFill("激励视频");
                    });
                }
                const closeHandler = (e: any) => {
                    this._videoAd.offClose(closeHandler);
                    SdkAudioAdapter.resumeMusic();
                    this.ispreLoadVideo = false;
                    if (e && e.isEnded || undefined === e) {
                        this._onPlayEnd && this._onPlayEnd(VideoAdCode.COMPLETE, "");
                    } else {
                        this._onPlayEnd && this._onPlayEnd(VideoAdCode.NOT_COMPLITE, "未完整观看视频广告");
                    }
                    this._onPlayEnd = null;
                };
                this._videoAd.onClose(closeHandler);
                this._videoAd.onError(() => {
                    this._onPlayEnd && this._onPlayEnd(VideoAdCode.AD_ERROR, "广告出现错误，请稍后重试");
                });
                SdkAudioAdapter.pauseMusic();
                this._videoAd.show().then(() => {
                    // ttPostbackCtl.GetInstance().adImpression("激励视频");
                }).catch((e: any) => {
                    console.log("广告组件出现问题，手动load一次", e);
                    this._videoAd.load().then(() => {
                        this._videoAd.show().catch(() => {
                            SdkAudioAdapter.resumeMusic();
                            this.ispreLoadVideo = false;
                            this._onPlayEnd && this._onPlayEnd(VideoAdCode.AD_ERROR, "视频显示异常，请稍后再试！");
                        });
                    }).catch(() => {
                        SdkAudioAdapter.resumeMusic();
                        this.ispreLoadVideo = false;
                        this._onPlayEnd && this._onPlayEnd(VideoAdCode.AD_ERROR, "视频加载异常，请稍后再试！");
                    });
                });
            }
        } else {
            this._onPlayEnd && this._onPlayEnd(VideoAdCode.NOT_SUPPORT, "不支持视频广告, 请更新头条版本");
        }
    }

    sendEvent(eventName: string, data?: any): void {
        if ("reward_btn" == eventName && cc.sys.getNetworkType() == cc.sys.NetworkType.NONE) {
            return;
        }
        Notifier.send(ListenID.Event_SendEvent, eventName, data);
    }

    share(shareId: string, shareData: any, successCallback?: Function, failCallback?: Function): void {
        if (shareData) {
            const shareConfig = GameUtil.weightGetValue(this.shareList, "weight");
            shareData.extra = shareData.extra || {};
            shareData.extra.withVideoId = !shareData.extra.withVideoId || shareData.extra.withVideoId;
            shareData.extra.hashtag_list = shareData.extra.hashtag_list ? shareData.extra.hashtag_list : ["割草", "肉鸽", "挑战", "休闲"];
            const shareOptions = {
                channel: shareData.channel || "article",
                extra: shareData.extra,
                title: shareConfig.title,
                desc: shareConfig.title,
                imageUrl: shareConfig.image,
                success: () => {
                    successCallback && successCallback();
                },
                fail: (e: any) => {
                    failCallback && failCallback(e);
                }
            };
            tt.shareAppMessage(shareOptions);
        }
    }

    showFullVideoAD(): void { }

    showInsertAd(adUnitId: string): void {
        if (tt.createInterstitialAd) {
            if (this.interstitialAd) {
                this.interstitialAd && this.interstitialAd.load();
            } else {
                const interstitialAd = tt.createInterstitialAd({
                    adUnitId: adUnitId
                });
                this.interstitialAd = interstitialAd;
                interstitialAd.onLoad(() => {
                    interstitialAd.show().then(() => {
                        console.log("插屏广告展示成功");
                    });
                });
                this.interstitialAd.onError((e: any) => {
                    console.log("插屏发生错误", e.errCode, e.errMsg);
                });
            }
        }
    }

    destroyInsertAd(): void {
        if (this.interstitialAd) {
            this.interstitialAd.destroy();
            this.interstitialAd = null;
        }
    }

    get _isByteDancePlatform(): boolean {
        return undefined !== tt;
    }

    isCanNavigateTo(): boolean {
        return this._isCanNavigateTo;
    }

    isCanAddDestop(): boolean {
        return this._isCanAddtoDestop;
    }

    vibrate(type: number = 0): void {
        if (0 == type) {
            tt.vibrateShort({
                success: () => { },
                fail: () => { }
            });
        } else {
            tt.vibrateLong({
                success: () => { },
                fail: () => { }
            });
        }
    }

    navigateToScene(scene: string = "sidebar"): void {
        tt.navigateToScene({
            scene: scene
        });
    }

    getScene(): string {
        return this._curscene;
    }

    checkSceneValue(scene: string): void {
        if (scene) {
            this._curscene = scene;
        }
    }

    addtodestop(): void {
        tt.addShortcut({
            success: () => {
                console.log("添加桌面成功");
                Manager.vo.knapsackVo.addGoods("TTScenc_destop");
                Notifier.send(ListenID.Main_ResetView);
            },
            fail: (e: any) => {
                console.log("添加桌面失败", e.errMsg);
            }
        });
    }

    getStorageItem(key: string): any {
        return tt.getStorageSync(key);
    }

    setStorageItem(key: string, value: any): void {
        tt.setStorageSync(key, value);
    }

    clearStorage(): void {
        tt.clearStorage({
            complete: (e: any) => {
                console.log("clearStorage", e);
            }
        });
    }

    removeStorageItem(key: string): void {
        tt.removeStorage({
            key: key,
            complete: (e: any) => {
                console.log("removeStorage", e);
            }
        });
    }
}
