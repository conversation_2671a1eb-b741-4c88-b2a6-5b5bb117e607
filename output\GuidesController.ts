// GuidesController.ts
// 从 GuidesController.js 转换而来

import { ListenID } from "./ListenID";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { KnapsackVo } from "./KnapsackVo";
import { GuidesModel } from "./GuidesModel";

/**
 * 引导控制器
 */
export class GuidesController extends MVC.Controller {
    private guidesModel: GuidesModel;

    constructor() {
        super();
        this.guidesModel = Manager.getModel(GuidesModel);
    }

    /**
     * 初始化监听
     */
    initListeners(): void {
        this.addListener(ListenID.GUIDE_START, this.onGuideStart, this);
        this.addListener(ListenID.GUIDE_NEXT, this.onGuideNext, this);
        this.addListener(ListenID.GUIDE_COMPLETE, this.onGuideComplete, this);
    }

    /**
     * 开始引导
     * @param guideId 引导ID
     */
    onGuideStart(guideId: number): void {
        this.guidesModel.startGuide(guideId);
        this.showGuideUI();
    }

    /**
     * 下一步引导
     */
    onGuideNext(): void {
        this.guidesModel.nextStep();
        this.updateGuideUI();
    }

    /**
     * 完成引导
     */
    onGuideComplete(): void {
        this.guidesModel.completeGuide();
        this.hideGuideUI();
    }

    /**
     * 显示引导UI
     */
    private showGuideUI(): void {
        UIManager.instance.showUI("GuideUI");
    }

    /**
     * 更新引导UI
     */
    private updateGuideUI(): void {
        Notifier.send(ListenID.GUIDE_UI_UPDATE);
    }

    /**
     * 隐藏引导UI
     */
    private hideGuideUI(): void {
        UIManager.instance.hideUI("GuideUI");
    }
}

export { GuidesController };