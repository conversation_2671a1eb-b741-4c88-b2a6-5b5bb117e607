var i;
var cc__extends = __extends;
var cc__assign = __assign;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Buff_ReplaceRole = exports.Buff_OnRoundState = exports.Buff_SetSlash = exports.Buff_ResistDamage = exports.Buff_AssociationProp = exports.Buff_Vampire = exports.Buff_OnSkill = exports.Buff_RestoreArmor = exports.Buff_OnBehitAddArmor = exports.Buff_OnSpawnHurtAddArmor = exports.Buff_OnLifeVal = exports.Buff_HitBack = exports.Buff_OnKillLayout = exports.Buff_ReboundDam = exports.Buff_OnSkillUseUnload = exports.Buff_AdrenalTechnology = exports.Buff_AtkFocus = exports.Buff_SubSkill = exports.Buff_OnUseSkillHurt = exports.Buff_Hurt = exports.Buff_Halo = exports.Buff_VicinityHurt = exports.Buff_EntityDead = exports.Buff_HPLinkOnce = exports.Buff_ContinuousRecovery = exports.Buff_HPLink = exports.Buff_OnKill = exports.Buff_OnBehit = exports.Buff_CurrencyReward = exports.Buff_OnVampirism = exports.Buff_OnSpawnHurt = exports.Buff_Effect = exports.Buff_OnTime = exports.Buff_Excute = exports.Buff_Default = undefined;
var $2Cfg = require("Cfg");
var $2GameatrCfg = require("GameatrCfg");
var $2Notifier = require("Notifier");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2AlertManager = require("AlertManager");
var $2Game = require("Game");
var $2PropertyVo = require("PropertyVo");
var $2Buff = require("Buff");
var _ = cc.v2();
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
var exp_Buff_Default = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  return cc__decorate([ccp_ccclass("Buff_Default")], _ctor);
}($2Buff.Buff.BuffItem);
exports.Buff_Default = exp_Buff_Default;
var exp_Buff_Excute = function (e) {
  function t() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.excuteTime = 1;
    t._excuteDt = 0;
    return t;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    this.otherValue && this.otherValue[0] && (this.excuteTime = Math.max(1, this.otherValue[0]));
    this.excute();
  };
  t.prototype.onUpdate = function (t) {
    if (this._isActive) {
      if ((this._excuteDt += t) > this.excuteTime) {
        this._excuteDt = 0, this.excute();
      }
      e.prototype.onUpdate.call(this, t);
    }
  };
  return cc__decorate([ccp_ccclass("Buff_Excute")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_Excute = exp_Buff_Excute;
var exp_Buff_OnTime = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    this.otherValue && this.otherValue[0] && (this.excuteTime = this.otherValue[0]);
    this.setLayer(0);
  };
  t.prototype.excute = function () {
    this.addLayer();
  };
  return cc__decorate([ccp_ccclass("Buff_OnTime")], t);
}(exp_Buff_Excute);
exports.Buff_OnTime = exp_Buff_OnTime;
var exp_Buff_Effect = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    var e = this;
    var t = this.owner.ID;
    var o = "entity/fight/effect/Buff_" + this.cutVo.id;
    this.cutEffectPath.push(o);
    $2Game.Game.Mgr.instance.showEffectByType(o, cc.Vec2.ZERO, true, -1, {
      parent: this.owner.topEffectBox
    }).then(function (o) {
      if (e.owner.isDead || t != e.owner.ID) {
        return o.removeEntityToUpdate();
      }
      e.cutEffect.push(o.node);
    });
    1001 == this.cutVo.id && (this.owner.roleNode.color = cc.color("#3EDCFF"));
  };
  t.prototype.unload = function () {
    e.prototype.unload.call(this);
    1001 == this.cutVo.id && (this.owner.roleNode.color = cc.Color.WHITE);
  };
  return cc__decorate([ccp_ccclass("Buff_Effect")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_Effect = exp_Buff_Effect;
var exp_Buff_OnSpawnHurt = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_SpawnHurt, this.onSpawnHurt, this);
  };
  t.prototype.onLoad = function () {
    this.cutVo.isOverlay > 1 && this.setLayer(0);
  };
  t.prototype.onSpawnHurt = function () {
    this.isWeight && this.addLayer();
  };
  return cc__decorate([ccp_ccclass("Buff_OnSpawnHurt")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnSpawnHurt = exp_Buff_OnSpawnHurt;
var exp_Buff_OnVampirism = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    this.setLayer(0);
  };
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_Vampirism, this.onVampirism, this);
  };
  t.prototype.onVampirism = function () {
    this.addLayer();
  };
  return cc__decorate([ccp_ccclass("Buff_OnVampirism")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnVampirism = exp_Buff_OnVampirism;
var exp_Buff_CurrencyReward = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.onLoad = function () {
    this.excute();
  };
  t.prototype.addLayer = function () {
    e.prototype.addLayer.call(this);
    this.excute();
  };
  t.prototype.excute = function () {
    var e = this.otherValue[0];
    this.game.knapsackMgr.addGoods(e, this.cutVo.value[0][0]);
    $2AlertManager.AlertManager.showNormalTips(cc.js.formatStr("获得%s %d", $2Cfg.Cfg.CurrencyConfig.get(e).name, this.cutVo.value[0][0]), {
      currencyID: e
    });
  };
  return cc__decorate([ccp_ccclass("Buff_CurrencyReward")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_CurrencyReward = exp_Buff_CurrencyReward;
var exp_Buff_OnBehit = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.onLoad = function () {
    this.cutVo.isOverlay > 1 && this.setLayer(0);
  };
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_BeHit, this.onBeHit, this);
  };
  t.prototype.onBeHit = function (e) {
    var t = this;
    if (this.isWeight) {
      this.addLayer();
      this.specialMap.filter(function (e) {
        return e.type == $2GameatrCfg.GameatrDefine.useSkill;
      }).forEach(function (o) {
        t.owner.skillMgr.useSubSkill(o.data, $2GameSeting.GameSeting.Release.OnBehit, {
          pos: e.owner.position
        });
      });
    }
  };
  return cc__decorate([ccp_ccclass("Buff_OnBehit")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnBehit = exp_Buff_OnBehit;
var exp_Buff_OnKill = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_Kill, this.onKill, this);
  };
  t.prototype.onKill = function () {};
  return cc__decorate([ccp_ccclass("Buff_OnKill")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnKill = exp_Buff_OnKill;
var exp_Buff_HPLink = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_BeHit, this.onBeHit, this);
  };
  t.prototype.onBeHit = function () {
    this.listener.curHpProgress < this.otherValue[0] && this.excute();
  };
  t.prototype.excute = function () {
    var e = this;
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
    this.specialMap.filter(function (e) {
      return e.type == $2GameatrCfg.GameatrDefine.addObjectBuff;
    }).forEach(function (t) {
      var o;
      null === (o = e.listener) || undefined === o || o.addBuff(t.data[0]);
    });
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
  };
  return cc__decorate([ccp_ccclass("Buff_HPLink")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_HPLink = exp_Buff_HPLink;
var exp_Buff_ContinuousRecovery = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var e = Math.ceil(this.owner.maxAllHp * this.attrMap.getor($2GameatrCfg.GameatrDefine.recover, 0));
    if (e) {
      var t = this.owner.getHurt().set({
        baseVal: Math.abs(e),
        critRate: 0
      });
      t.hitPos.set(this.owner.position);
      this.caster && (t.owner = this.caster);
      if (e > 0) {
        this.owner.treat(t);
      } else {
        this.owner.behit(t);
      }
    }
  };
  return cc__decorate([ccp_ccclass("Buff_ContinuousRecovery")], t);
}(exp_Buff_Excute);
exports.Buff_ContinuousRecovery = exp_Buff_ContinuousRecovery;
var exp_Buff_HPLinkOnce = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
  };
  t.prototype.onFight_RoundState = function () {
    1 == this.game.bronMonsterMgr.cutStatus && this.addLayer();
  };
  t.prototype.excute = function () {
    if (0 != this.buffLayer) {
      e.prototype.excute.call(this);
      this.unuseLayer();
    }
  };
  return cc__decorate([ccp_ccclass("Buff_HPLinkOnce")], t);
}(exp_Buff_HPLink);
exports.Buff_HPLinkOnce = exp_Buff_HPLinkOnce;
var exp_Buff_EntityDead = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_Dead, this.onDead, this);
  };
  t.prototype.onDead = function () {
    var e;
    if (this.isWeight) {
      var t = this.attrMap.getor($2GameatrCfg.GameatrDefine.monsterSplit, 0);
      var o = this.owner.position.clone();
      if (t > 0) {
        var i = this.owner.lvCfg;
        var n = cc__assign(cc__assign({}, i), {
          bronTime: .2,
          bronSpeed: 1,
          exp: 0,
          monId: [this.cutVo.value[0][1]],
          Count: this.cutVo.value[0][0],
          monsterID: this.cutVo.value[0][1],
          dropExpRatio: null
        });
        var a = $2GameUtil.GameUtil.getDesignSize.width / this.game.gameCamera.cutZoomRatio * .4;
        for (var s = 0; s < n.Count; s++) {
          var l = o.add($2GameUtil.GameUtil.AngleAndLenToPos($2Game.Game.random(0, 360), 50));
          l.x = cc.misc.clampf(l.x, -a, a);
          this.game.bronMonsterMgr.createMonster(n, l);
        }
      }
      var u = this.attrMap.getor($2GameatrCfg.GameatrDefine.areaDam, 0);
      if (u) {
        var p = this.caster ? Math.ceil(this.caster.property.cut.atk * (this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0) + u)) : 1;
        (null === (e = this.caster) || undefined === e ? undefined : e.buffMgr) && (p *= 1 + this.caster.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.skilldam, 0));
        var f = this.owner.getHurt().set({
          baseVal: p,
          hasBehitEffect: false
        });
        f.hitPos.set(this.owner.position);
        this.game.LatticeElementMap.seekByPos({
          pos: this.owner.position,
          targetCamp: this.targetCamp,
          radius: this.cutVo.effectArea
        }).forEach(function (e) {
          e.behit(f);
        });
      }
      this.showBuffEffect();
    }
  };
  return cc__decorate([ccp_ccclass("Buff_EntityDead")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_EntityDead = exp_Buff_EntityDead;
var exp_Buff_VicinityHurt = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    var e;
    var t = this.caster ? Math.ceil(this.caster.property.cut.atk * this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0)) : 1;
    (null === (e = this.caster) || undefined === e ? undefined : e.buffMgr) && (t *= 1 + this.caster.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.skilldam, 0));
    var o = this.owner.getHurt().set({
      baseVal: t,
      hasBehitEffect: false
    });
    o.hitPos.set(this.owner.position);
    this.game.LatticeElementMap.seekByPos({
      pos: this.owner.position,
      targetCamp: this.targetCamp,
      radius: this.cutVo.effectArea
    }).forEach(function (e) {
      e.behit(o);
    });
  };
  return cc__decorate([ccp_ccclass("Buff_VicinityHurt")], t);
}(exp_Buff_Excute);
exports.Buff_VicinityHurt = exp_Buff_VicinityHurt;
var exp_Buff_Halo = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    var e = this;
    this.cutVo.attr.forEach(function (t) {
      e.attrMap.set(t, 0);
    });
  };
  t.prototype.unload = function () {
    e.prototype.unload.call(this);
  };
  t.prototype.excute = function () {
    var e = this;
    var t = this.cutVo;
    var o = {
      id: 100 * t.id,
      name: t.name + "光环属性",
      type: t.type,
      time: this.excuteTime,
      attr: t.attr,
      value: t.value,
      ishideUI: 1,
      skillId: t.skillId,
      icon: t.icon
    };
    $2Game.Game.mgr.elementMap.forEach(function (i) {
      i.isActive && (t.effectArea && cc.Vec2.squaredDistance(e.owner.position, i.position) > Math.pow(t.effectArea, 2) || (1 == t.object && i.campType != e.owner.campType ? i.addBuffByData(o) : 2 == t.object && i.campType == e.owner.campType ? i.addBuffByData(o) : 3 == t.object && i.addBuffByData(o)));
    });
  };
  return cc__decorate([ccp_ccclass("Buff_Halo")], t);
}(exp_Buff_Excute);
exports.Buff_Halo = exp_Buff_Halo;
var exp_Buff_Hurt = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    e.prototype.onLoad.call(this);
  };
  t.prototype.excute = function () {
    var e;
    this.hurtVal = this.caster ? Math.ceil(this.caster.property.cut.atk * this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0)) : 1;
    (null === (e = this.caster) || undefined === e ? undefined : e.buffMgr) && (this.hurtVal *= 1 + this.caster.buffMgr.getSpecificBuffAttr({
      buffID: this.cutVo.id
    }).getor($2GameatrCfg.GameatrDefine.buffDam, 0));
    var t = this.caster.getHurt().set({
      baseVal: this.hurtVal,
      owner: this.caster,
      hasBehitEffect: false
    });
    t.hitPos.set(this.owner.position);
    this.owner.behit(t);
    this.showBuffEffect();
  };
  t.prototype.unload = function () {
    e.prototype.unload.call(this);
  };
  return cc__decorate([ccp_ccclass("Buff_Hurt")], t);
}(exp_Buff_Excute);
exports.Buff_Hurt = exp_Buff_Hurt;
var exp_Buff_OnUseSkillHurt = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(t.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_EntityUseSkillEnd, this.onEntityUseSkill, this);
  };
  t.prototype.onEntityUseSkill = function () {
    this.hurt.baseVal = this.role.maxAllHp * this.attrMap.getor($2GameatrCfg.GameatrDefine.buffDam, 0);
    this.hurt.hitPos.set(this.owner.position);
    this.role.behit(this.hurt);
  };
  t.prototype.onLoad = function () {
    e.prototype.onLoad.call(this);
    this.hurt = this.owner.getHurt().set({
      baseVal: 0,
      owner: this.owner
    });
  };
  t.prototype.unload = function () {
    e.prototype.unload.call(this);
    this.hurt = null;
  };
  return cc__decorate([ccp_ccclass("Buff_OnUseSkillHurt")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnUseSkillHurt = exp_Buff_OnUseSkillHurt;
var exp_Buff_SubSkill = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {};
  return cc__decorate([ccp_ccclass("Buff_SubSkill")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_SubSkill = exp_Buff_SubSkill;
var exp_Buff_AtkFocus = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    this.setLayer(0);
    this.oldVictim = null;
  };
  t.prototype.onSpawnHurt = function (e, t) {
    var o = this;
    t.node.once($2ListenID.ListenID.Fight_Dead, function () {
      o.setLayer(0);
      o.oldVictim = null;
    }, t.node);
    if (null == this.oldVictim || this.oldVictim == t.ID) {
      this.addLayer();
    } else {
      this.setLayer(0);
    }
    this.oldVictim = t.ID;
  };
  return cc__decorate([ccp_ccclass("Buff_AtkFocus")], t);
}(exp_Buff_OnSpawnHurt);
exports.Buff_AtkFocus = exp_Buff_AtkFocus;
var exp_Buff_AdrenalTechnology = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {};
  t.prototype.onSpawnHurt = function () {
    var e = this;
    if (this.isWeight) {
      var t = this.cutVo;
      var o = {
        id: 100 * t.id,
        name: t.name + "光环属性",
        type: 3,
        time: t.otherValue[0],
        attr: [$2GameatrCfg.GameatrDefine.skilldam],
        value: [[this.attrMap.get($2GameatrCfg.GameatrDefine.damadd)]],
        ishideUI: 1,
        res: ["bones/skill/fx_buff_ad", "2"]
      };
      $2Game.Game.mgr.elementMap.forEach(function (t) {
        t.isActive && t.campType == e.owner.campType && t.addBuffByData(o);
      });
      this.game.mainRole.addBuffByData(o);
    }
  };
  return cc__decorate([ccp_ccclass("Buff_AdrenalTechnology")], t);
}(exp_Buff_OnSpawnHurt);
exports.Buff_AdrenalTechnology = exp_Buff_AdrenalTechnology;
var exp_Buff_OnSkillUseUnload = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_EntityUseSkillEnd, this.onEntityUseSkill, this);
  };
  t.prototype.onEntityUseSkill = function () {
    this.mgr.clearBuff(this);
  };
  return cc__decorate([ccp_ccclass("Buff_OnSkillUseUnload")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnSkillUseUnload = exp_Buff_OnSkillUseUnload;
var exp_Buff_ReboundDam = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {};
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_BeHit, this.onBeHit, this);
  };
  t.prototype.onBeHit = function (e) {
    var t;
    var o = this;
    if (this.isWeight) {
      var i = e.owner;
      if (i.isActive) {
        var n = this.cutVo.effectArea ? this.game.LatticeElementMap.seekByPos({
          pos: i.position,
          targetCamp: [i.campType],
          radius: this.cutVo.effectArea
        }) : [i];
        this.owner.delayByGame(function () {
          n.forEach(function (e) {
            var t = e.curHp * o.mgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.ReboundDamPer, 0) + o.attrMap.getor($2GameatrCfg.GameatrDefine.ReboundDamVal, 0);
            var i = o.owner.getHurt().set({
              baseVal: Math.abs(t),
              critRate: 0
            });
            e.isActive && e.behit(i);
          });
        }, .5);
      }
      var r = $2Cfg.Cfg.BulletEffect.get(null === (t = this.otherValue) || undefined === t ? undefined : t[0]);
      r && $2Manager.Manager.loader.loadSpineNode(r.spine[0], {
        nodeAttr: {
          parent: this.game._topEffectNode,
          position: i.position
        },
        spAttr: {
          defaultAnimation: r.spine[1] || "animation",
          loop: false
        },
        delayRemove: 2
      });
    }
  };
  return cc__decorate([ccp_ccclass("Buff_ReboundDam")], t);
}(exp_Buff_OnBehit);
exports.Buff_ReboundDam = exp_Buff_ReboundDam;
var exp_Buff_OnKillLayout = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    e.prototype.onLoad.call(this);
    this.setLayer(0);
  };
  t.prototype.onKill = function () {
    this.addLayer();
  };
  return cc__decorate([ccp_ccclass("Buff_OnKillLayout")], t);
}(exp_Buff_OnKill);
exports.Buff_OnKillLayout = exp_Buff_OnKillLayout;
var exp_Buff_HitBack = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    e.prototype.onLoad.call(this);
    this._hitBackDis = this.attrMap.getor($2GameatrCfg.GameatrDefine.repeled, 0);
    this._hitBackDirection = this.owner.position.sub(this.caster.position).normalize();
    this._hitBackDirection.x = 0;
  };
  t.prototype.onUpdate = function (t) {
    if (this._hitBackDirection) {
      e.prototype.onUpdate.call(this, t);
      cc.Vec2.multiplyScalar(_, this._hitBackDirection, this._hitBackDis * t);
      cc.Vec2.add(_, _, this.owner.position);
      this.owner.setPosition(_);
    }
  };
  t.prototype.unload = function () {
    this._hitBackDirection = null;
    e.prototype.unload.call(this);
  };
  return cc__decorate([ccp_ccclass("Buff_HitBack")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_HitBack = exp_Buff_HitBack;
var exp_Buff_OnLifeVal = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onBeHit = function () {
    this.setLayer(this.owner.curHpProgress <= this.otherValue[0] ? 1 : 0);
  };
  return cc__decorate([ccp_ccclass("Buff_OnLifeVal")], t);
}(exp_Buff_OnBehit);
exports.Buff_OnLifeVal = exp_Buff_OnLifeVal;
var exp_Buff_OnSpawnHurtAddArmor = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onSpawnHurt = function () {
    if (this.isWeight) {
      var e = this.attrMap.getor($2GameatrCfg.GameatrDefine.shieldval, 0) * (1 + this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.shieldPer, 0));
      this.owner.addArmor(e);
    }
  };
  return cc__decorate([ccp_ccclass("Buff_OnSpawnHurtAddArmor")], t);
}(exp_Buff_OnSpawnHurt);
exports.Buff_OnSpawnHurtAddArmor = exp_Buff_OnSpawnHurtAddArmor;
var exp_Buff_OnBehitAddArmor = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onBeHit = function () {
    if (this.isWeight) {
      var e = this.attrMap.getor($2GameatrCfg.GameatrDefine.shieldval, 0) * (1 + this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.shieldPer, 0));
      this.owner.addArmor(e);
    }
  };
  return cc__decorate([ccp_ccclass("Buff_OnBehitAddArmor")], t);
}(exp_Buff_OnBehit);
exports.Buff_OnBehitAddArmor = exp_Buff_OnBehitAddArmor;
var exp_Buff_RestoreArmor = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.excute = function () {
    if (this.isWeight) {
      var e = this.attrMap.getor($2GameatrCfg.GameatrDefine.shieldval, 0) * (1 + this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.shieldPer, 0));
      this.owner.addArmor(e);
    }
  };
  return cc__decorate([ccp_ccclass("Buff_RestoreArmor")], t);
}(exp_Buff_Excute);
exports.Buff_RestoreArmor = exp_Buff_RestoreArmor;
var exp_Buff_OnSkill = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    this.cutVo.isOverlay > 1 && this.setLayer(0);
  };
  t.prototype.changeListener = function (e) {
    var t;
    null === (t = this.listener) || undefined === t || t.node.changeListener(e, $2ListenID.ListenID.Fight_OnSkill, this.onSkill, this);
  };
  t.prototype.onSkill = function () {
    this.isWeight && this.addLayer();
  };
  return cc__decorate([ccp_ccclass("Buff_OnSkill")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnSkill = exp_Buff_OnSkill;
var exp_Buff_Vampire = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onSpawnHurt = function (e, t) {
    if (this.isWeight && e.val > 0) {
      var o = Math.ceil(e.val * this.owner.buffMgr.attrMapAll.getor($2GameatrCfg.GameatrDefine.vampireEffect, 0));
      var i = this.owner.getHurt().set({
        baseVal: Math.abs(o),
        critRate: 0
      });
      this.owner.treat(i);
      this.owner.node.emit($2ListenID.ListenID.Fight_Vampirism, i, t);
    }
  };
  return cc__decorate([ccp_ccclass("Buff_Vampire")], t);
}(exp_Buff_OnSpawnHurt);
exports.Buff_Vampire = exp_Buff_Vampire;
var exp_Buff_AssociationProp = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  Object.defineProperty(t.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(t.prototype, "role", {
    get: function () {
      return this.game.mainRole;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(t.prototype, "pack", {
    get: function () {
      return this.game.packView;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.onLoad = function () {
    var t = this;
    e.prototype.onLoad.call(this);
    var o = this.pack.propList.filter(function (e) {
      return t.otherValue.includes(e.mergeCfg.id);
    });
    o.push.apply(o, this.pack.StoreList.arr);
    this.setLayer(o.length > 0 ? 1 : 0);
  };
  return cc__decorate([ccp_ccclass("Buff_AssociationProp")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_AssociationProp = exp_Buff_AssociationProp;
var exp_Buff_ResistDamage = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    this.setLayer(this.cutVo.isOverlay);
  };
  t.prototype.unuseLayer = function (t) {
    undefined === t && (t = -1);
    this.showBuffEffect();
    e.prototype.unuseLayer.call(this, t);
    0 == this.buffLayer && this.mgr.clearBuff(this);
  };
  return cc__decorate([ccp_ccclass("Buff_ResistDamage")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_ResistDamage = exp_Buff_ResistDamage;
var exp_Buff_SetSlash = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    var e = this.caster.getHurt().set({
      baseVal: 999999999,
      critRate: 0,
      type: $2PropertyVo.Hurt.Type.Slash
    });
    this.owner.behit(e);
    this.game.showEffectByPath(this.cutVo.res[0], {
      nodeAttr: {
        parent: this.game._topEffectNode,
        position: this.owner.position,
        scale: 1
      },
      spAttr: {
        defaultAnimation: "animation",
        premultipliedAlpha: false,
        loop: false
      },
      delayRemove: 1
    });
    this.mgr.clearBuff(this);
  };
  return cc__decorate([ccp_ccclass("Buff_SetSlash")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_SetSlash = exp_Buff_SetSlash;
var exp_Buff_OnRoundState = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.changeListener = function (t) {
    e.prototype.changeListener.call(this, t);
    $2Notifier.Notifier.changeListener(t, $2ListenID.ListenID.Fight_RoundState, this.onFight_RoundState, this);
  };
  t.prototype.onFight_RoundState = function () {};
  return cc__decorate([ccp_ccclass("Buff_OnRoundState")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_OnRoundState = exp_Buff_OnRoundState;
var exp_Buff_ReplaceRole = function (e) {
  function t() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(t, e);
  t.prototype.onLoad = function () {
    var e = this.attrMap.get($2GameatrCfg.GameatrDefine.replaceRole);
    var t = $2Cfg.Cfg.RoleUnlock.get(e);
    var o = $2Game.ModeCfg.Role.find({
      roleId: e,
      lv: 1
    });
    var i = this.owner;
    i.skillMgr.clearByID(i.myData.startSkill, true);
    i.myData.startBuff && i.buffMgr.clearBuffByID(i.myData.startBuff);
    i.myData = t;
    i.skillMgr.add(t.startSkill, true, true);
    t.startBuff && i.buffMgr.add(t.startBuff);
    i.property.set(o);
    i.updateProperty();
    i.initHp();
    this.mgr.clearBuff(this);
  };
  return cc__decorate([ccp_ccclass("Buff_ReplaceRole")], t);
}($2Buff.Buff.BuffItem);
exports.Buff_ReplaceRole = exp_Buff_ReplaceRole;