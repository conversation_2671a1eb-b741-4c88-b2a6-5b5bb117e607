const fs = require('fs');
const path = require('path');

// 检查文件是否包含 ccclass
function containsCcclass(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查 ccclass 特征
        const ccclassPatterns = [
            /cc__decorator\.ccclass/,           // cc__decorator.ccclass
            /ccp_ccclass\s*=\s*cc__decorator\.ccclass/, // ccp_ccclass = cc__decorator.ccclass
            /@ccclass/,                        // @ccclass 装饰器
            /cc__decorate\s*\(\s*\[\s*ccp_ccclass/, // cc__decorate([ccp_ccclass
            /cc__decorate\s*\(\s*\[\s*ccclass/     // cc__decorate([ccclass
        ];
        
        // 计算 ccclass 数量
        let ccclassCount = 0;
        const ccclassMatches = content.match(/cc__decorate\s*\(\s*\[\s*ccp_ccclass/g) || [];
        ccclassCount = ccclassMatches.length;
        
        // 检查是否包含任何 ccclass 特征
        const hasCcclass = ccclassPatterns.some(pattern => pattern.test(content));
        
        // 查找类定义
        const classDefinitions = content.match(/var\s+(def_|exp_)(\w+)\s*=\s*function\s*\(/g) || [];
        const exportsDefinitions = content.match(/exports\.(\w+)\s*=\s*undefined/g) || [];
        
        return {
            filePath: filePath,
            fileName: path.basename(filePath),
            hasCcclass: hasCcclass,
            ccclassCount: ccclassCount,
            classDefinitions: classDefinitions.length,
            exportsDefinitions: exportsDefinitions.length,
            totalDefinitions: classDefinitions.length + exportsDefinitions.length,
            isSingleClass: (classDefinitions.length + exportsDefinitions.length) === 1,
            isMultiClass: (classDefinitions.length + exportsDefinitions.length) > 1
        };
    } catch (error) {
        return {
            filePath: filePath,
            fileName: path.basename(filePath),
            error: error.message,
            hasCcclass: false
        };
    }
}

// 移动文件
function moveFile(sourcePath, targetPath) {
    try {
        fs.renameSync(sourcePath, targetPath);
        return true;
    } catch (error) {
        console.error(`移动文件失败: ${sourcePath} -> ${targetPath}`, error.message);
        return false;
    }
}

// 获取文件列表
function getJSFiles(dirPath) {
    if (!fs.existsSync(dirPath)) {
        return [];
    }
    return fs.readdirSync(dirPath)
        .filter(file => file.endsWith('.js'))
        .map(file => path.join(dirPath, file));
}

// 主函数
function main() {
    const scripts5Dir = './scripts_5';
    const scriptsCcsDir = './scripts_ccs';
    
    // 确保目标文件夹存在
    if (!fs.existsSync(scriptsCcsDir)) {
        fs.mkdirSync(scriptsCcsDir, { recursive: true });
        console.log(`创建目标文件夹: ${scriptsCcsDir}`);
    }
    
    // 获取 scripts_5 中的所有 JS 文件
    const jsFiles = getJSFiles(scripts5Dir);
    
    console.log(`分析 scripts_5 文件夹中的 ${jsFiles.length} 个 JS 文件...\n`);
    
    const ccclassFiles = [];
    const nonCcclassFiles = [];
    const errorFiles = [];
    
    // 分析每个文件
    jsFiles.forEach(filePath => {
        const analysis = containsCcclass(filePath);
        
        if (analysis.error) {
            errorFiles.push(analysis);
            console.log(`❌ 错误: ${analysis.fileName} - ${analysis.error}`);
            return;
        }
        
        if (analysis.hasCcclass) {
            ccclassFiles.push(analysis);
            const typeInfo = analysis.isSingleClass ? '单ccclass' : 
                           analysis.isMultiClass ? '多ccclass' : 'ccclass';
            console.log(`✅ ${typeInfo}: ${analysis.fileName} (ccclass数: ${analysis.ccclassCount}, 总定义: ${analysis.totalDefinitions})`);
        } else {
            nonCcclassFiles.push(analysis);
            console.log(`❌ 非ccclass: ${analysis.fileName} (总定义: ${analysis.totalDefinitions})`);
        }
    });
    
    console.log(`\n=== 分析结果 ===`);
    console.log(`包含 ccclass 的文件: ${ccclassFiles.length} 个`);
    console.log(`不包含 ccclass 的文件: ${nonCcclassFiles.length} 个`);
    console.log(`错误文件: ${errorFiles.length} 个`);
    
    if (ccclassFiles.length === 0) {
        console.log('\n没有发现包含 ccclass 的文件。');
        return;
    }
    
    // 显示将要移动的文件
    console.log(`\n=== 将要移动到 scripts_ccs 的文件 ===`);
    ccclassFiles.forEach((file, index) => {
        const typeInfo = file.isSingleClass ? '[单]' : file.isMultiClass ? '[多]' : '[?]';
        console.log(`${index + 1}. ${file.fileName} ${typeInfo} (ccclass: ${file.ccclassCount})`);
    });
    
    console.log(`\n开始移动包含 ccclass 的文件...\n`);
    
    let successCount = 0;
    let failCount = 0;
    
    // 移动文件
    ccclassFiles.forEach((fileInfo, index) => {
        const fileName = fileInfo.fileName;
        const sourcePath = fileInfo.filePath;
        const targetPath = path.join(scriptsCcsDir, fileName);
        
        console.log(`[${index + 1}/${ccclassFiles.length}] 移动: ${fileName}`);
        console.log(`  ccclass 数量: ${fileInfo.ccclassCount}, 总定义: ${fileInfo.totalDefinitions}`);
        
        // 检查目标文件是否已存在
        if (fs.existsSync(targetPath)) {
            console.log(`  ⚠️ 目标文件已存在，跳过移动`);
            return;
        }
        
        if (moveFile(sourcePath, targetPath)) {
            successCount++;
            console.log(`  ✓ 成功移动`);
        } else {
            failCount++;
            console.log(`  ✗ 移动失败`);
        }
    });
    
    console.log(`\n=== 移动完成 ===`);
    console.log(`成功移动: ${successCount} 个文件`);
    console.log(`移动失败: ${failCount} 个文件`);
    console.log(`已存在跳过: ${ccclassFiles.length - successCount - failCount} 个文件`);
    
    // 显示移动后的统计
    const remainingFiles = getJSFiles(scripts5Dir);
    const scriptsCcsFiles = getJSFiles(scriptsCcsDir);
    
    console.log(`\nscripts_5 文件夹现在剩余 ${remainingFiles.length} 个 JS 文件`);
    console.log(`scripts_ccs 文件夹现在包含 ${scriptsCcsFiles.length} 个 JS 文件`);
    
    // 按 ccclass 数量分类显示移动的文件
    const singleCcclassFiles = ccclassFiles.filter(f => f.isSingleClass);
    const multiCcclassFiles = ccclassFiles.filter(f => f.isMultiClass);
    
    console.log(`\n=== 移动文件分类 ===`);
    console.log(`单 ccclass 文件: ${singleCcclassFiles.length} 个`);
    console.log(`多 ccclass 文件: ${multiCcclassFiles.length} 个`);
    
    if (multiCcclassFiles.length > 0) {
        console.log(`\n=== 多 ccclass 文件详情 ===`);
        multiCcclassFiles.forEach(file => {
            console.log(`${file.fileName} - ccclass数: ${file.ccclassCount}, 总定义: ${file.totalDefinitions}`);
        });
    }
    
    // 生成报告
    const report = {
        timestamp: new Date().toISOString(),
        totalAnalyzed: jsFiles.length,
        ccclassFiles: ccclassFiles.length,
        nonCcclassFiles: nonCcclassFiles.length,
        errorFiles: errorFiles.length,
        successfullyMoved: successCount,
        failedToMove: failCount,
        singleCcclassFiles: singleCcclassFiles.length,
        multiCcclassFiles: multiCcclassFiles.length,
        remainingInScripts5: remainingFiles.length,
        totalInScriptsCcs: scriptsCcsFiles.length,
        movedFiles: ccclassFiles.map(f => ({
            fileName: f.fileName,
            ccclassCount: f.ccclassCount,
            totalDefinitions: f.totalDefinitions,
            isSingleClass: f.isSingleClass,
            isMultiClass: f.isMultiClass
        }))
    };
    
    fs.writeFileSync('all_ccclass_move_report.json', JSON.stringify(report, null, 2));
    console.log(`\n移动报告已保存到: all_ccclass_move_report.json`);
}

main();
