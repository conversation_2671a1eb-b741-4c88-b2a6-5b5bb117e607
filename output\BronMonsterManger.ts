import { ListenID } from "./ListenID";
import { Cfg } from "./Cfg";
import { Notifier } from "./Notifier";
import { GameUtil } from "./GameUtil";
import { NodePool } from "./NodePool";
import { Game, ModeCfg } from "./Game";
import { Monster } from "./Monster";
import { default as SkillModel } from "./SkillModel";
import { RewardEvent } from "./RewardEvent";

const tempVec2 = cc.Vec2.ZERO;

interface MonsterLvConfig {
    lv: number;
    round: number;
    monId: number[];
    Count: number;
    bronSpeed: number;
    letTime: number;
    createNum: number;
    type: number;
    monsterID: number;
}

interface RoundMonster extends MonsterLvConfig {
    letTime: number;
    createNum: number;
    type: number;
    monsterID: number;
}

export class BronMonsterManger extends cc.Component {
    public level: number = 1;
    private _batchNum: number = 1;
    private _batchTime: number = 0;
    private _batchSumCount: number = 0;
    public batchInterval: number[] = [80, 80, 80, 80, 80, 80, 80, 80];
    public maxCount: number = 600;
    private _maxLen: number = 0;
    public isBossRound: boolean = false;
    private _RoundMonster: RoundMonster[] = [];
    
    public MonsterType: { [key: number]: string } = {
        1: "Monster",
        2: "MonsterElite", 
        3: "MonsterBoss",
        4: "CanWreckItem"
    };
    
    private _monTime: number = 0;
    private _eventTime: number = 0;
    private _dtTime: number = 0;
    private _remain: number = 0;
    public RewardEvent: RewardEvent.Manager;

    constructor() {
        super();
        this.level = 1;
        this._batchNum = 1;
        this._batchTime = 0;
        this._batchSumCount = 0;
        this.batchInterval = [80, 80, 80, 80, 80, 80, 80, 80];
        this.maxCount = 600;
        this._maxLen = 0;
        this.isBossRound = false;
        this._RoundMonster = [];
        this._monTime = 0;
        this._eventTime = 0;
        this._dtTime = 0;
        this._remain = 0;
    }

    get mainRole(): any {
        return this.game.mainRole;
    }

    get batchNum(): number {
        return this._batchNum;
    }

    set batchNum(value: number) {
        this._batchNum = value;
        this._batchTime = this.batchInterval[value - 1];
        this.RoundMonster = ModeCfg.MonsterLv.filter({
            lv: this.level,
            round: this._batchNum
        });
        Notifier.send(ListenID.Fight_GameRound, value);
        Notifier.send(ListenID.Fight_CountDown, this._batchTime);
    }

    get RoundMonster(): RoundMonster[] {
        return this._RoundMonster;
    }

    set RoundMonster(monsters: MonsterLvConfig[]) {
        this._RoundMonster.length = 0;
        monsters.push(...ModeCfg.MonsterLv.filter({ lv: -1 }));
        
        monsters.forEach((monster) => {
            const monsterId = GameUtil.getRandomInArray(monster.monId, 1)[0];
            const monsterConfig = Cfg.Monster.get(monsterId);
            
            if (monsterConfig) {
                this._RoundMonster.push({
                    letTime: 0,
                    createNum: 0,
                    type: monsterConfig.type,
                    monsterID: monsterId,
                    ...monster
                });
            }
        });
    }

    onEnable(): void {
        this.changeListener(true);
    }

    onDisable(): void {
        this.changeListener(false);
    }

    changeListener(enable?: boolean): void {
        // Listener implementation
    }

    init(): void {
        this._maxLen = Math.max(cc.winSize.width, cc.winSize.height) / 2;
        this.batchNum = 1;
        SkillModel.getInstance.cutLevelSkill = ModeCfg.Skiilpool.get(this.level);
        this.RewardEvent = new RewardEvent.Manager(this);
        this.RewardEvent.createEventList();
    }

    get game(): any {
        return Game.mgr;
    }

    get role(): any {
        return this.game.mainRole;
    }

    get randomD(): cc.Vec2 {
        cc.Vec2.UP.rotate(Math.PI * (2 * Math.random() - 1), tempVec2);
        return tempVec2;
    }

    get randomL(): number {
        return Game.random(this._maxLen + 100, this._maxLen + 200);
    }

    get randomPos(): { x: number; y: number } {
        const pos = this.mainRole.position.add(this.randomD.mul(this.randomL));
        return { x: pos.x, y: pos.y };
    }

    getMonSpPos(param1: any, param2: number = 0): any {
        return null;
    }

    isArriveOn(): boolean {
        return false;
    }

    getRandomPos(distance: number): { x: number; y: number } {
        const pos = this.mainRole.position.add(this.randomD.mul(distance));
        return { x: pos.x, y: pos.y };
    }

    getRandomPosByAngle(angle: number, distance: number): { x: number; y: number } {
        cc.Vec2.UP.rotate(angle + 30, tempVec2);
        const pos = this.mainRole.position.add(tempVec2.mul(distance));
        return { x: pos.x, y: pos.y };
    }

    addMonsterTide(config: MonsterLvConfig): void {
        cc.log("生成兽潮");
        Notifier.send(ListenID.Fight_GameMonsterCome);

        let index = 0;
        const rolePos = this.game.mainRole.position;
        const distance = this.randomL;

        Game.timer(() => {
            tempVec2 = rolePos.add(GameUtil.AngleAndLenToPos(6 * index, distance + (index % 2) * 200));
            this.createMonster(config, { x: tempVec2.x, y: tempVec2.y }).then((monster) => {
                monster.toMove();
            });
            index++;
        }, 0.05, config.Count || 60);
    }

    addSprintMonster(config: MonsterLvConfig): void {
        const angles = GameUtil.getRandomInArray([45, 135, 225, 315], Game.random(2, 4));
        const distance = this.randomL;

        angles.forEach((angle, index) => {
            Game.timerOnce(() => {
                tempVec2 = this.mainRole.position.add(this.mainRole.forwardDirection.normalize().mul(100));
                const startPos = GameUtil.AngleAndLenToPos(angle, 1.5 * distance).add(tempVec2);
                const endPos = GameUtil.AngleAndLenToPos(angle + 180, 2 * distance).add(tempVec2);

                Game.timer(() => {
                    this.createMonster(config, { x: startPos.x, y: startPos.y }).then((monster) => {
                        monster.toSprint(endPos);
                    });
                }, 0, config.Count || 60);
            }, index);
        });
    }

    getMonsterSetType(config?: MonsterLvConfig): any {
        return null;
    }

    createMonster(config: MonsterLvConfig, position: { x: number; y: number }, monsterClass?: any): Promise<any> {
        return new Promise((resolve) => {
            const monsterConfig = Cfg.Monster.get(config.monsterID);

            if (!monsterConfig) {
                console.error("没有找到怪物配置", config.name);
                return;
            }

            if (this.game._entityNode.childrenCount > this.maxCount) {
                return;
            }

            if (monsterConfig.type === 3) {
                Notifier.send(ListenID.Fight_ShowGameTips, 3, "Boss来袭");
            }

            let nodeItem = NodePool.spawn("entity/fight/" + this.MonsterType[monsterConfig.type]);
            const customType = this.getMonsterSetType(config);
            if (customType) {
                nodeItem = customType;
            }

            nodeItem.setNodeAssetFinishCall((node: cc.Node) => {
                if (!node) {
                    console.error("怪物生成错误", monsterConfig?.name);
                    return;
                }

                if (monsterClass) {
                    const existingMonster = node.getComponent(Monster);
                    if (existingMonster && existingMonster.constructor.name !== monsterClass.toString().match(/^function\s+([a-zA-Z_$][0-9a-zA-Z_$]*)\s*\(/)?.[1]) {
                        existingMonster.destroy();
                    }
                } else {
                    monsterClass = monsterClass || Monster;
                }

                const monster = node.getORaddComponent(monsterClass);
                node.setAttribute({
                    parent: this.game._entityNode,
                    active: true,
                    opacity: 255
                });

                const pos = cc.v2(position.x, position.y);
                monster.setPosition(pos);
                monster.monsterId = +monsterConfig.id;
                monster.lvCfg = config;
                monster.init();
                monster.steering?.setTargetAgent1(this.mainRole);

                this.game._monsterMap.set(monster.ID, monster);
                this.game.elementMap.set(monster.ID, monster);
                resolve(monster);
            });
        });
    }

    addMonsterById(config: MonsterLvConfig, position?: { x: number; y: number }, delay: number = 0): void {
        Game.timer(() => {
            this.createMonster(config, position || this.randomPos).then((monster) => {
                monster.toMove();
            });
        }, config.bronSpeed, config.Count);
        this._batchSumCount--;
    }

    onUpdate(): void {
        // Update logic
    }

    onDestroy(): void {
        this.RewardEvent = null;
    }
}
