import { Cfg } from "./Cfg";
import { MVC } from "./MVC";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { GameUtil } from "./GameUtil";
import { default as DialogBox } from "./DialogBox";
import { Game } from "./Game";
import { NodePool } from "./NodePool";
import { default as SettingModel } from "./SettingModel";
import { default as NormalTips } from "./NormalTips";

export enum AlertType {
    COMMON = "0",
    SELECT = "1"
}

export enum ItemAlertType {
    Tips = 0,
    NotEnough = 1
}

interface AlertOptions {
    title?: string;
    desc?: string;
    confirmText?: string;
    cancelText?: string;
    icon?: string;
    viewType?: any;
    hasIgnore?: boolean;
    confirm?: () => void;
    cancel?: () => void;
}

interface ItemAlertOptions {
    type: ItemAlertType;
    id: string;
    confirm?: () => void;
}

interface TipsOptions {
    currencyID?: string;
    time?: number;
    ydis?: number;
    pos?: cc.Vec2;
    isgameUI?: boolean;
    parent?: cc.Node;
    delayTime?: number;
}

interface HurtTipsOptions {
    parent?: cc.Node;
    color?: cc.Color;
    position?: cc.Vec2;
}

export class AlertManager {
    private static canShow: boolean = true;
    private static tipPool = new cc.NodePool("NormalTips");
    private static TipsTextPool = new cc.NodePool();

    static showAlert(type: AlertType, options: AlertOptions): void {
        const openArgs = new MVC.OpenArgs();
        openArgs.setParam(options);
        
        if (type === AlertType.COMMON) {
            UIManager.Open("ui/common/alert/CommonAlert", openArgs);
        } else if (type === AlertType.SELECT) {
            UIManager.Open("ui/common/alert/SelectAlert", openArgs);
        }
    }

    static showPropertyChange(nodes: cc.Node[], properties: any, offset: cc.Vec2 = cc.Vec2.ZERO): void {
        let index = 0;
        for (const key in properties) {
            const node = nodes[index] || nodes[0];
            const value = properties[key];
            const delay = 0.15 * index++;
            
            cc.tween(nodes[0])
                .delay(delay)
                .call(() => {
                    // Property change animation logic
                })
                .start();
        }
    }

    static showCommonTips(text: string, worldPos: cc.Vec2, maxWidth: number = 250, fontSize: number = 16): void {
        UIManager.Open("ui/common/CommonTipsView", 
            MVC.openArgs()
                .setIsNeedLoading(false)
                .setParam({
                    str: text,
                    worldPos: worldPos,
                    maxWidth: maxWidth,
                    fontSize: fontSize
                })
        );
    }

    static showCommonAlert(options: AlertOptions): void {
        this.showAlert(AlertType.COMMON, options);
    }

    static showSelectAlert(options: AlertOptions): void {
        this.showAlert(AlertType.SELECT, options);
    }

    static showItemAlert(options: ItemAlertOptions): void {
        const today = GameUtil.dateFormat(Date.now());
        
        if (options.type === ItemAlertType.NotEnough) {
            options.confirm = () => {};
        }
        
        const storageKey = `ItemAlert_${options.type}_${options.id}`;
        if (today === Manager.storage.getString(storageKey)) {
            return options.confirm?.();
        }
        
        UIManager.Open("ui/common/alert/ItemAlert", MVC.openArgs().setParam(options));
    }

    static showCommonGetResourceConfirmView(options: any): void {
        UIManager.Open("ui/common/CommonGetResourceConfirmView", MVC.openArgs().setParam(options));
    }

    static showNormalTipsOnce(text: string, parent: cc.Node = null, time: number = 0.7,
                             distance: number = 50, position: cc.Vec2 = cc.Vec2.ZERO): void {
        if (this.canShow) {
            this.canShow = false;
            const tipNode = this.tipPool.get();

            const setupTip = (node: cc.Node) => {
                node.getComponent(NormalTips).setText(text);
                const iconNode = node.children[0]?.children[0];
                iconNode?.setActive(false);

                node.position = position;
                node.opacity = 20;

                if (parent && cc.isValid(parent)) {
                    node.parent = parent;
                } else {
                    node.setParent(UIManager.layerRoots(MVC.eUILayer.Tips));
                }

                const moveTween = cc.tween().by(time, { position: cc.v3(0, distance) });
                cc.tween(node)
                    .parallel(
                        moveTween,
                        cc.tween().to(0.4, { opacity: 255 })
                    )
                    .delay(0.5)
                    .by(time, {
                        position: cc.v2(0, distance),
                        opacity: -254
                    })
                    .call(() => {
                        this.tipPool.put(node);
                        this.canShow = true;
                    })
                    .start();
            };

            if (tipNode) {
                setupTip(tipNode);
            } else {
                Manager.loader.loadPrefab("ui/common/alert/tip").then((prefab: cc.Node) => {
                    setupTip(prefab);
                });
            }
        }
    }

    static showNormalTips(text: string, options: TipsOptions = {}): void {
        const currencyConfig = options.currencyID && Cfg.CurrencyConfig.get(options.currencyID);
        options.time = options.time || 0.3;
        options.ydis = options.ydis || 50;
        options.pos = options.pos || cc.Vec2.ZERO;

        const prefabPath = options.isgameUI ? "ui/common/alert/gametip" : "ui/common/alert/tip";

        Manager.loader.loadPrefab(prefabPath).then((prefab: cc.Node) => {
            const iconSprite = prefab.getComByPath(cc.Sprite, "bg/icon");
            iconSprite?.node.setActive(!!currencyConfig);

            if (currencyConfig) {
                Manager.loader.loadSpriteToSprit(currencyConfig.icon, iconSprite);
            }

            prefab.getComponent(NormalTips).setText(text);
            prefab.opacity = 255;

            if (cc.isValid(options.parent)) {
                prefab.setParent(options.parent);
                prefab.setPosition(0, 0);
            } else {
                prefab.setParent(UIManager.layerRoots(MVC.eUILayer.Guide));
            }

            prefab.position = options.pos;

            cc.tween(prefab)
                .parallel(
                    cc.tween().by(options.time, { position: cc.v2(0, options.ydis) }),
                    cc.tween().to(0.3, { opacity: 255 })
                )
                .delay(options.delayTime || 1)
                .by(options.time, {
                    position: cc.v2(0, 2 * options.ydis),
                    opacity: -254
                })
                .call(() => {
                    this.tipPool.put(prefab);
                    this.canShow = true;
                })
                .start();
        });
    }

    static showAlertDesc(text: string, position: cc.Vec2 = cc.Vec2.ZERO): void {
        const setupDesc = (node: cc.Node) => {
            const normalTips = node.getComponent(NormalTips).setText(text);
            position.x *= 0.8;
            node.position = position;
            node.opacity = 20;
            node.group = "UI";

            const label = normalTips.label;
            label.string = text;
            node.setParent(UIManager.layerRoots(MVC.eUILayer.Guide));

            if (text.length > 6) {
                label.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
            } else {
                label.overflow = cc.Label.Overflow.NONE;
            }

            label.scheduleOnce(() => {
                node.children[0].height = label.node.height;
            });

            const moveTween = cc.tween().by(0.3, { position: cc.v3(0, 30) });
            cc.tween(node)
                .parallel(
                    moveTween,
                    cc.tween().to(0.4, { opacity: 255 })
                )
                .delay(0.6)
                .by(0.3, {
                    position: cc.v2(0, 50),
                    opacity: -254
                })
                .call(() => {
                    this.tipPool.put(node);
                    this.canShow = true;
                })
                .start();
        };

        Manager.loader.loadPrefab("ui/common/alert/AlertDesc").then((prefab: cc.Node) => {
            setupDesc(prefab);
        });
    }

    static showHurtTips(damage: number, options: HurtTipsOptions, isCrit: boolean = false): void {
        if (SettingModel.instance.toggle.hurtTips) {
            const tipNode = NodePool.spawn("ui/common/" + (isCrit ? "CritTips" : "NumTips"));

            tipNode.setNodeAssetFinishCall((node: cc.Node) => {
                options.parent = options.parent || Game.mgr.topUINode;
                options.color = options.color || cc.Color.WHITE;

                if (cc.isValid(options.parent)) {
                    const label = node.children[0];
                    label.color = options.color;

                    if (isCrit) {
                        damage = "p" + damage as any;
                    }

                    label.getComponent(cc.Label).string = "" + damage;
                    node.setAttribute({
                        active: false,
                        opacity: 255,
                        angle: 0,
                        group: "Game",
                        ...options
                    });

                    if (tipNode.spawner.runNum > 50) {
                        Game.tween(node)
                            .set({
                                active: true,
                                scale: 5,
                                opacity: 255
                            })
                            .parallel(
                                cc.tween()
                                    .to(0.1, { scale: isCrit ? 4 : 3 })
                                    .to(0.2, { scale: 1, opacity: 50 }),
                                cc.tween()
                                    .by(0.1, {
                                        x: Game.random(-60, 60),
                                        y: Game.random(30, 100)
                                    })
                                    .by(0.1, { y: -10 })
                            )
                            .call(() => {
                                NodePool.despawn(node.nodeItem);
                            })
                            .start();
                    } else {
                        Game.tween(node)
                            .set({
                                active: true,
                                scale: 0,
                                opacity: 255
                            })
                            .to(0.05, { scale: isCrit ? 4 : 3 })
                            .parallel(
                                cc.tween().to(0.3, { scale: 1 }),
                                cc.tween().by(0.6, {
                                    x: Manager.random.randomInt(-100, 100)
                                }, { easing: cc.easing.sineOut }),
                                cc.tween()
                                    .by(0.2, { y: 80 }, { easing: cc.easing.sineOut })
                                    .by(0.4, {
                                        opacity: -255,
                                        scale: -1,
                                        y: -80
                                    })
                            )
                            .call(() => {
                                NodePool.despawn(node.nodeItem);
                            })
                            .start();
                    }
                } else {
                    NodePool.despawn(node.nodeItem);
                }
            });
        }
    }

    static showDialogBox(text: string | number, options: HurtTipsOptions): void {
        if (SettingModel.instance.toggle.hurtTips) {
            NodePool.spawn("ui/common/DialogBox").setNodeAssetFinishCall((node: cc.Node) => {
                options.parent = options.parent || Game.mgr.topUINode;
                options.position = options.position || cc.v2(0, 100);

                if (cc.isValid(options.parent)) {
                    node.getComponent(DialogBox).string = text.toString();
                    node.setAttribute({
                        opacity: 255,
                        angle: 0,
                        scale: 0,
                        group: options.parent.group,
                        ...options
                    });
                } else {
                    NodePool.despawn(node.nodeItem);
                }
            });
        }
    }

    static showTipsText(text: string, options: any): void {
        const tipNode = this.TipsTextPool.get();

        const setupTipsText = (node: cc.Node) => {
            if (cc.isValid(parent)) {
                node.setAttribute({
                    active: false,
                    opacity: 255,
                    angle: 0,
                    ...options
                });

                node.getComponent(cc.Label).string = text;

                Game.tween(node)
                    .set({
                        active: true,
                        scale: 0,
                        opacity: 255
                    })
                    .to(0.2, { scale: 1 })
                    .delay(0.2)
                    .by(0.3, {
                        opacity: -255,
                        scale: -2,
                        y: 80
                    })
                    .call(() => {
                        this.TipsTextPool.put(node);
                    })
                    .start();
            }
        };

        if (tipNode) {
            setupTipsText(tipNode);
        } else {
            Manager.loader.loadPrefab("ui/common/alert/TipsText").then((prefab: cc.Node) => {
                setupTipsText(prefab);
            });
        }
    }
}
