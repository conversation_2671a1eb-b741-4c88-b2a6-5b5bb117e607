Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GridViewFreshWork = exports.GridViewFreshWorkItem = undefined;
var $2Time = require("Time");
var exp_GridViewFreshWorkItem = function () {};
exports.GridViewFreshWorkItem = exp_GridViewFreshWorkItem;
var exp_GridViewFreshWork = function () {
  function _ctor() {
    this.gapTime = .02;
    this._timeWatcher = null;
    this.dic = [];
  }
  _ctor.prototype.removeWork = function (e) {
    var t = 0;
    for (var o = this.getWorkItems(e); t < o.length; t++) {
      var i = o[t];
      this.dic.splice(i, 1);
    }
  };
  _ctor.prototype.clear = function () {
    this.dic.length = 0;
  };
  _ctor.prototype.addWork = function (e, t) {
    var o = new exp_GridViewFreshWorkItem();
    o.index = e;
    o.function = t;
    this.dic.push(o);
    this.run();
  };
  _ctor.prototype.run = function () {
    if (null === this._timeWatcher) {
      this._timeWatcher = $2Time.Time.delay(this.gapTime, this.excute, null, this, -1);
      this.excute();
    }
  };
  _ctor.prototype.excute = function () {
    if (0 === this.dic.length) {
      this._timeWatcher && $2Time.Time.doCancel(this._timeWatcher);
      return void (this._timeWatcher = null);
    }
    this.dic[0].function();
    this.dic.shift();
  };
  _ctor.prototype.getWorkItems = function (e) {
    var t = [];
    for (var o = 0; o < this.dic.length; o++) {
      this.dic[o].index === e && t.push(o);
    }
    return t;
  };
  return _ctor;
}();
exports.GridViewFreshWork = exp_GridViewFreshWork;