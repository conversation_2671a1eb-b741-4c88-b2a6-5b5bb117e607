// GridViewFreshWork.ts
// 从 GridViewFreshWork.js 转换而来

import { Time } from "./Time";

/**
 * 网格视图刷新工作项
 */
export class GridViewFreshWorkItem {
    public freshTime: number = 0;
    public freshFunc: Function = null;

    constructor() {
        // 初始化
    }
}

/**
 * 网格视图刷新工作管理器
 */
export class GridViewFreshWork {
    private static _instance: GridViewFreshWork = null;
    private workList: GridViewFreshWorkItem[] = [];

    public static get instance(): GridViewFreshWork {
        if (!this._instance) {
            this._instance = new GridViewFreshWork();
        }
        return this._instance;
    }

    constructor() {
        this.workList = [];
    }

    /**
     * 添加刷新工作
     * @param freshTime 刷新时间
     * @param freshFunc 刷新函数
     */
    addFreshWork(freshTime: number, freshFunc: Function): void {
        const workItem = new GridViewFreshWorkItem();
        workItem.freshTime = freshTime;
        workItem.freshFunc = freshFunc;
        this.workList.push(workItem);
    }

    /**
     * 更新
     */
    update(): void {
        for (let i = this.workList.length - 1; i >= 0; i--) {
            const workItem = this.workList[i];
            workItem.freshTime -= Time.deltaTime;
            if (workItem.freshTime <= 0) {
                if (workItem.freshFunc) {
                    workItem.freshFunc();
                }
                this.workList.splice(i, 1);
            }
        }
    }
}

export { GridViewFreshWorkItem };