var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ModeBulletsReboundController = undefined;
var $2CallID = require("CallID");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2ListenID = require("ListenID");
var $2Time = require("Time");
var $2UIManager = require("UIManager");
var $2NodePool = require("NodePool");
var $2ModeBulletsReboundModel = require("ModeBulletsReboundModel");
var exp_ModeBulletsReboundController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t.setup($2ModeBulletsReboundModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "ModeBulletsReboundController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Replay, this.onReplay, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_NextLV, this.onNextLV, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_BackToMain, this.backToMain, this);
  };
  _ctor.prototype.closeGame = function () {
    if (this._model.gameMode == this.cutMode) {
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
      $2UIManager.UIManager.Close("ui/ModeBulletsRebound/M32_FightUIView");
      $2UIManager.UIManager.Close("ui/ModeBulletsRebound/M32_FightScene");
    }
  };
  _ctor.prototype.backToMain = function () {
    if (this._model.gameMode == this.cutMode) {
      this.closeGame();
      $2NodePool.NodePool.clear();
    }
  };
  Object.defineProperty(_ctor.prototype, "cutMode", {
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onOpenGame = function (e, t) {
    if (this._model.gameMode == e) {
      this.oldArgs = t;
      t.setIsNeedLoading(false).setNodeGroup(0);
      $2UIManager.UIManager.Open("ui/ModeBulletsRebound/M32_FightScene", t);
    }
  };
  _ctor.prototype.onNextLV = function (e) {
    var t = this;
    if (this._model.gameMode == this.cutMode) {
      this.closeGame();
      $2NodePool.NodePool.clear();
      $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, true);
      $2Time.Time.delay(1, function () {
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        $2Notifier.Notifier.send($2ListenID.ListenID.Game_Load, t.mode.gameMode, e);
      });
    }
  };
  _ctor.prototype.onReplay = function () {
    var e = this;
    if (this._model.gameMode == this.cutMode) {
      this.closeGame();
      $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, true);
      $2Time.Time.delay(1, function () {
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_LoadingView, false);
        $2Notifier.Notifier.send($2ListenID.ListenID.Game_Load, e.mode.gameMode, e.oldArgs);
      });
    }
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.ModeBulletsReboundController = exp_ModeBulletsReboundController;