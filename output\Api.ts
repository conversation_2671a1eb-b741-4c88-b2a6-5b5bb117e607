import { default as Request } from "./Request";
import { DOMAIN } from "./config";
import { md5 } from "./md51";
import { SdkConfig } from "./SdkConfig";

interface RequestData {
    [key: string]: any;
}

type SuccessCallback = (response: any) => void;
type FailCallback = (error: any) => void;
type CompleteCallback = () => void;

export function click(
    data: RequestData,
    success: SuccessCallback = () => {},
    fail: FailCallback = () => {},
    complete: CompleteCallback = () => {}
): void {
    Request.post({
        url: DOMAIN + "/common/app-track/click",
        data: data,
        dataType: "json",
        success: success,
        fail: fail,
        complete: complete
    });
}

export function report(
    data: RequestData,
    success: SuccessCallback = () => {},
    fail: FailCallback = () => {},
    complete: CompleteCallback = () => {}
): void {
    const traceId = md5((Math.round(Date.now() / 1000) + 1000 * Math.random()).toString());
    
    Request.post({
        url: DOMAIN + "/common/user-op/op-merge-report?trace_id=" + traceId,
        data: data,
        dataType: "json",
        success: success,
        fail: fail,
        complete: complete
    });
}

export function getOpenid(
    data: RequestData,
    success: SuccessCallback = () => {},
    fail: FailCallback = () => {},
    complete: CompleteCallback = () => {}
): void {
    const platformUrls: { [key: number]: string } = {};
    platformUrls[SdkConfig.EPlatform.KWAI_MICRO] = DOMAIN + "/common/kuaishou/login";
    platformUrls[SdkConfig.EPlatform.BYTE_DANCE] = DOMAIN + "/common/tt/session/sign_in";
    platformUrls[SdkConfig.EPlatform.WECHAT_GAME] = DOMAIN + "/common/session/check_code";
    
    Request.post({
        url: platformUrls[wonderSdk.platformId],
        data: data,
        dataType: "json",
        success: success,
        fail: fail,
        complete: complete
    });
}

export function serverTime(
    success: SuccessCallback = () => {},
    fail: FailCallback = () => {},
    complete: CompleteCallback = () => {}
): void {
    Request.get({
        url: DOMAIN + "/common/common/time",
        dataType: "json",
        success: success,
        fail: fail,
        complete: complete
    });
}
