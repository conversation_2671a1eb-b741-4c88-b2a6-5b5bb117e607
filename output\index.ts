// index.ts
// 从 index.js 转换而来

import { Report } from "./Report";
import { Show } from "./Show";
import { Hide } from "./Hide";
import { Login } from "./Login";
import { Api } from "./Api";
import { TimeManage } from "./TimeManage";
import { config } from "./config";
import { ReportQueue } from "./ReportQueue";
import { Params } from "./Params";
import { LocalStorage } from "./LocalStorage";
import { EventModel } from "./EventModel";

// 声明全局变量
declare global {
    interface Window {
        wx: any;
        tt: any;
        qq: any;
        qg: any;
        swan: any;
        my: any;
    }
}

/**
 * 小游戏SDK检测
 */
const minSdk = window.wx || window.tt || window.qq || window.qg || window.swan || window.my || null;

/**
 * 主入口类
 * 负责初始化和管理整个应用
 */
export default class GameIndex {
    /** 是否已初始化 */
    private static initialized: boolean = false;
    
    /** SDK实例 */
    public static sdk: any = minSdk;
    
    /** 配置信息 */
    public static config = config;
    
    /**
     * 初始化应用
     */
    static init(): void {
        if (this.initialized) {
            console.warn("应用已经初始化过了");
            return;
        }
        
        console.log("开始初始化应用...");
        
        // 初始化各个模块
        this.initModules();
        
        // 初始化SDK
        this.initSDK();
        
        // 初始化事件系统
        this.initEventSystem();
        
        // 初始化数据存储
        this.initStorage();
        
        // 初始化时间管理
        this.initTimeManage();
        
        // 初始化API
        this.initAPI();
        
        // 初始化上报系统
        this.initReportSystem();
        
        this.initialized = true;
        console.log("应用初始化完成");
    }
    
    /**
     * 初始化模块
     */
    private static initModules(): void {
        console.log("初始化模块...");
        
        // 初始化显示/隐藏管理
        Show.init();
        Hide.init();
        
        // 初始化登录模块
        Login.init();
    }
    
    /**
     * 初始化SDK
     */
    private static initSDK(): void {
        if (this.sdk) {
            console.log("检测到小游戏环境:", this.getSDKType());
            
            // 根据不同平台进行特殊初始化
            switch (this.getSDKType()) {
                case "wx":
                    this.initWechatSDK();
                    break;
                case "tt":
                    this.initToutiaoSDK();
                    break;
                case "qq":
                    this.initQQSDK();
                    break;
                case "qg":
                    this.initOppoSDK();
                    break;
                case "swan":
                    this.initBaiduSDK();
                    break;
                case "my":
                    this.initAlipaySDK();
                    break;
            }
        } else {
            console.log("运行在Web环境");
        }
    }
    
    /**
     * 获取SDK类型
     */
    private static getSDKType(): string {
        if (window.wx) return "wx";
        if (window.tt) return "tt";
        if (window.qq) return "qq";
        if (window.qg) return "qg";
        if (window.swan) return "swan";
        if (window.my) return "my";
        return "web";
    }
    
    /**
     * 初始化微信SDK
     */
    private static initWechatSDK(): void {
        console.log("初始化微信小游戏SDK");
        // 微信特殊初始化逻辑
    }
    
    /**
     * 初始化头条SDK
     */
    private static initToutiaoSDK(): void {
        console.log("初始化头条小游戏SDK");
        // 头条特殊初始化逻辑
    }
    
    /**
     * 初始化QQ SDK
     */
    private static initQQSDK(): void {
        console.log("初始化QQ小游戏SDK");
        // QQ特殊初始化逻辑
    }
    
    /**
     * 初始化OPPO SDK
     */
    private static initOppoSDK(): void {
        console.log("初始化OPPO小游戏SDK");
        // OPPO特殊初始化逻辑
    }
    
    /**
     * 初始化百度SDK
     */
    private static initBaiduSDK(): void {
        console.log("初始化百度小游戏SDK");
        // 百度特殊初始化逻辑
    }
    
    /**
     * 初始化支付宝SDK
     */
    private static initAlipaySDK(): void {
        console.log("初始化支付宝小游戏SDK");
        // 支付宝特殊初始化逻辑
    }
    
    /**
     * 初始化事件系统
     */
    private static initEventSystem(): void {
        console.log("初始化事件系统...");
        EventModel.init();
    }
    
    /**
     * 初始化数据存储
     */
    private static initStorage(): void {
        console.log("初始化数据存储...");
        LocalStorage.init();
    }
    
    /**
     * 初始化时间管理
     */
    private static initTimeManage(): void {
        console.log("初始化时间管理...");
        TimeManage.init();
    }
    
    /**
     * 初始化API
     */
    private static initAPI(): void {
        console.log("初始化API...");
        Api.init();
    }
    
    /**
     * 初始化上报系统
     */
    private static initReportSystem(): void {
        console.log("初始化上报系统...");
        Report.init();
        ReportQueue.init();
    }
    
    /**
     * 获取应用信息
     */
    static getAppInfo(): {
        platform: string;
        initialized: boolean;
        hasSDK: boolean;
        version: string;
    } {
        return {
            platform: this.getSDKType(),
            initialized: this.initialized,
            hasSDK: !!this.sdk,
            version: config.version || "1.0.0"
        };
    }
    
    /**
     * 检查是否为小游戏环境
     */
    static isMiniGame(): boolean {
        return !!this.sdk;
    }
    
    /**
     * 检查是否为Web环境
     */
    static isWeb(): boolean {
        return !this.sdk;
    }
    
    /**
     * 获取平台特定功能
     */
    static getPlatformFeatures(): {
        hasShare: boolean;
        hasAd: boolean;
        hasPayment: boolean;
        hasVibrate: boolean;
    } {
        const platform = this.getSDKType();
        
        return {
            hasShare: platform !== "web",
            hasAd: ["wx", "tt", "qq"].includes(platform),
            hasPayment: ["wx", "my"].includes(platform),
            hasVibrate: platform !== "web"
        };
    }
    
    /**
     * 销毁应用
     */
    static destroy(): void {
        console.log("销毁应用...");
        
        // 清理各个模块
        TimeManage.destroy();
        ReportQueue.destroy();
        EventModel.destroy();
        
        this.initialized = false;
        console.log("应用已销毁");
    }
}

// 导出SDK实例和配置
export { minSdk, config };

// 自动初始化
if (typeof window !== "undefined") {
    // 在浏览器环境中自动初始化
    GameIndex.init();
}
