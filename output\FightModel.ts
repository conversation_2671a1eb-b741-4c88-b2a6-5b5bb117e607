import { MVC } from "./MVC";
import { Game } from "./Game";

export default class FightModel extends MVC.BaseModel {
    private static _instance: FightModel | null = null;
    public cutMode: any = Game.Mode.NONE;

    constructor() {
        super();
        if (FightModel._instance == null) {
            FightModel._instance = this;
        }
    }

    reset(): void {}

    static get getInstance(): FightModel {
        if (FightModel._instance == null) {
            FightModel._instance = new FightModel();
        }
        return FightModel._instance;
    }

    init(): void {}
}
