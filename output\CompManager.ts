import { Game } from "./Game";

export default class CompManager {
    private static _instance: CompManager | null = null;
    private _compMap: Map<any, any> = new Map();

    static get Instance(): CompManager {
        if (CompManager._instance == null) {
            CompManager._instance = new CompManager();
        }
        return CompManager._instance;
    }

    registerComp(comp: any): void {
        if (this._compMap.has(comp.ID)) {
            cc.warn("CompManager.registerComp repeat", comp.ID);
        } else {
            this._compMap.set(comp.ID, comp);
        }
    }

    getCompById(id: any): any {
        return this._compMap.get(id);
    }

    removeComp(comp: any): void {
        this._compMap.delete(comp.ID);
    }

    get compMap(): Map<any, any> {
        return this._compMap;
    }

    clear(): void {
        this._compMap.clear();
    }

    get game(): any {
        return Game.mgr;
    }

    onUpdate(deltaTime: number): void {
        this._compMap.forEach((comp: any) => {
            comp.onUpdate(deltaTime);
        });
    }
}
