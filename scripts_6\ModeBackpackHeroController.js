var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.ModeBackpackHeroController = undefined;
var $2CallID = require("CallID");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2Time = require("Time");
var $2UIManager = require("UIManager");
var $2BaseNet = require("BaseNet");
var $2WonderSdk = require("WonderSdk");
var $2KnapsackVo = require("KnapsackVo");
var $2EventModel = require("EventModel");
var $2Game = require("Game");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var exp_ModeBackpackHeroController = function (e) {
  function _ctor() {
    var t = e.call(this) || this;
    t._isBackFromTryPlay = false;
    t.setup($2ModeBackpackHeroModel.default.instance);
    t.changeListener(true);
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.reset = function () {
    this._model.reset();
  };
  Object.defineProperty(_ctor.prototype, "classname", {
    get: function () {
      return "ModeBackpackHeroController";
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.registerAllProtocol = function () {};
  _ctor.prototype.changeListener = function (e) {
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Login_Finish, this.loginFinish, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Game_Load, this.onOpenGame, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Fight_BackToMain, this.backToMain, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.M20_CheckMenu, this.showMenu, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.M20_ShowEquipInfo, this.showEquipInfo, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.M20_ShowRoleInfo, this.showRoleInfo, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.M20_CheckEnergy, this.checkEnergy, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Shop_BuyCharge, this.onBuyCharge, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.M20_CheckPackData, this.checkDailyData, this);
    $2Notifier.Notifier.changeListener(e, $2ListenID.ListenID.Is_Back_From_Try_Play, this.setIsBackFromTry, this);
  };
  _ctor.prototype.closeGame = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
    $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_FightBuffView");
    $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_Fight_PackView");
    $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_FightUIView");
    $2UIManager.UIManager.Close("ui/ModeBackpackHero/M20_FightScene");
  };
  _ctor.prototype.loginFinish = function () {
    this.checkFrom();
    this.mode.fragmentsPack = new $2KnapsackVo.KnapsackVo.Mgr(true, "bph_fragmentspack");
    this.mode.fightinfopack = new $2KnapsackVo.KnapsackVo.Mgr(true, "bph_fightinfopack");
    this.mode.shopGoodspck = new $2KnapsackVo.KnapsackVo.Mgr(true, "bph_shopGoodspck");
    this.mode.dailyAdpack = new $2KnapsackVo.KnapsackVo.Mgr(true, "bph_dailyAdpackpack");
    this.mode.loginFinish();
    this.checkDailyData();
  };
  _ctor.prototype.onBuyCharge = function (e) {
    if (200 == e.id) {
      this.mode.rVo.dailyData.sweep_count += 1;
      this.mode.recordVo.SaveData();
    }
    if (201 == e.id) {
      this.mode.rVo.dailyData.sweep_count += 3;
      this.mode.recordVo.SaveData();
    }
  };
  _ctor.prototype.checkDailyData = function () {
    var e = this;
    if ($2Manager.Manager.vo.userVo.dailyData.isnewday) {
      this.mode.dailyAdpack.forEach(function (t) {
        e.mode.dailyAdpack.del(t.id);
      });
      this.mode.shopGoodspck.forEach(function (t) {
        e.mode.shopGoodspck.del(t.id);
      });
      this.mode.dailyAdpack.addGoods("energycoin", $2Manager.Manager.vo.switchVo.diamondBuyStamina[2]);
      this.mode.dailyAdpack.addGoods("energyad", $2Manager.Manager.vo.switchVo.adBuyStamina[2]);
      var t = new Date($2Time.Time.serverTimeMs);
      $2Notifier.Notifier.send($2ListenID.ListenID.Task_ClearData, 1);
      1 == t.getDay() && $2Notifier.Notifier.send($2ListenID.ListenID.Task_ClearData, 2);
      $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateProgress, 1);
      $2Notifier.Notifier.send($2ListenID.ListenID.Task_UpdateNextDayView);
      cc.sys.localStorage.setItem("saveShopList", []);
    }
    this.checkEnergy();
    this.mode.dailyAdpack.SaveData();
    if (!this.mode.fightinfopack.has("role")) {
      this.mode.unlockRole(3e3);
      this.mode.useRole(3e3);
    }
  };
  _ctor.prototype.checkEnergy = function () {
    cc.log("enter:", $2Manager.Manager.vo.userVo.shop_refreshTimestamp);
    var e = Math.floor($2Manager.Manager.vo.userVo.shop_refreshTimestamp / 1e3);
    var t = Math.floor(new Date().getTime() / 1e3);
    if (0 != e) {
      var o = t - e;
      $2Manager.Manager.vo.userVo.dailyData.shop_refreshCd -= o;
      var i = 0;
      var n = $2ModeBackpackHeroModel.default.instance.energyMinoffset;
      if (o >= $2Manager.Manager.vo.userVo.energy_refreshCd) {
        o -= $2Manager.Manager.vo.userVo.energy_refreshCd;
        $2Manager.Manager.vo.userVo.energy_refreshCd = 0;
        i = 1;
      }
      var r = o - (i += Math.floor(o / (60 * n))) * n * 60;
      if ($2Manager.Manager.vo.knapsackVo.getVal($2CurrencyConfigCfg.CurrencyConfigDefine.Energy) < 30) {
        var s = 30 - $2Manager.Manager.vo.knapsackVo.getVal($2CurrencyConfigCfg.CurrencyConfigDefine.Energy);
        i > s && (i = s);
        $2Manager.Manager.vo.knapsackVo.addGoods($2CurrencyConfigCfg.CurrencyConfigDefine.Energy, i);
        $2Manager.Manager.vo.knapsackVo.getVal($2CurrencyConfigCfg.CurrencyConfigDefine.Energy) < 30 && ($2Manager.Manager.vo.userVo.energy_refreshCd -= r);
      }
    }
    this.mode.ischeckdone = true;
  };
  _ctor.prototype.showMenu = function (e, t, o) {
    undefined === o && (o = false);
    var i = $2MVC.MVC.openArgs();
    i.setParam(t);
    i.setIsNeedLoading(true);
    $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_PrePare_" + e, i);
  };
  _ctor.prototype.showEquipInfo = function (e) {
    $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_EquipInfo", $2MVC.MVC.openArgs().setParam(e));
  };
  _ctor.prototype.showRoleInfo = function (e) {
    $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_Pop_RoleInfo", $2MVC.MVC.openArgs().setParam(e));
  };
  _ctor.prototype.setIsBackFromTry = function () {
    this._isBackFromTryPlay = true;
  };
  Object.defineProperty(_ctor.prototype, "isBackFromTryPlay", {
    get: function () {
      var e = this._isBackFromTryPlay;
      this._isBackFromTryPlay = false;
      return e;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.backToMain = function () {
    if (this.mode.curActivity) {
      $2UIManager.UIManager.OpenInQueue(this.mode.curActivity.prefab);
    } else if (!this.isBackFromTryPlay && (this.cutMode == $2Game.Game.Mode.PICKUPBULLETS || this.cutMode == $2Game.Game.Mode.THROWINGKNIFE || this.cutMode == $2Game.Game.Mode.BULLETSREBOUND || this.cutMode == $2Game.Game.Mode.MANGUARDS || this.cutMode == $2Game.Game.Mode.TIDEDEFEND || this.cutMode == $2Game.Game.Mode.ALLOUTATTACK)) {
      var e = $2MVC.MVC.openArgs();
      e.setParam({
        pageIndex: this.cutMode
      });
      $2UIManager.UIManager.Open("ui/setting/MoreGamesView", e);
    }
    this.closeGame();
    1 == this.mode.PlayingLv && this.mode.firstLvCheck();
    $2Notifier.Notifier.send($2ListenID.ListenID.BottomBar_OpenView, 1);
  };
  Object.defineProperty(_ctor.prototype, "cutMode", {
    get: function () {
      return $2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode);
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onOpenGame = function (e, t) {
    if (this._model.gameMode == e) {
      this.oldArgs = t;
      t.setIsNeedLoading(false).setNodeGroup(0);
      $2UIManager.UIManager.Open("ui/ModeBackpackHero/M20_FightScene", t);
    }
  };
  _ctor.prototype.checkFrom = function () {
    if ($2EventModel.default.instance.userVo.mid3) {
      return new Promise(function (e, t) {
        $2BaseNet.BaseNet.Request($2BaseNet.BaseUrl.ServerDomain + $2BaseNet.Url.MATERIALSS, {
          app_name: $2WonderSdk.WonderSdk._instance.BMS_APP_NAME,
          version: $2WonderSdk.WonderSdk._instance.BMS_VERSION,
          channel: $2WonderSdk.WonderSdk._instance.CHANNEL_NAME,
          mid: $2EventModel.default.instance.userVo.mid3
        }, "GET").then(function (t) {
          cc.log(t);
          $2ModeBackpackHeroModel.default.instance.adGameSS = t.data.url;
          cc.log($2ModeBackpackHeroModel.default.instance.adGameSS);
          $2Notifier.Notifier.send($2ListenID.ListenID.ResetView);
          e(t);
        }).catch(function (e) {
          t(e);
        });
      });
    }
  };
  return _ctor;
}($2MVC.MVC.MController);
exports.ModeBackpackHeroController = exp_ModeBackpackHeroController;