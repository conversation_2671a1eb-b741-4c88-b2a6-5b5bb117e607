const fs = require('fs');
const path = require('path');

// 创建输出目录
const outputDir = 'output_5_auto';
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// 获取所有 JS 文件
const scriptsDir = 'scripts_5';
const files = fs.readdirSync(scriptsDir).filter(file => file.endsWith('.js'));

console.log(`Found ${files.length} files to process`);

function restoreFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    let restoredLines = [];
    let i = 0;
    
    // 跳过编译器生成的变量声明
    while (i < lines.length && (
        lines[i].startsWith('var ') && (lines[i].includes('__') || lines[i].trim() === 'var i;') ||
        lines[i].trim() === ''
    )) {
        i++;
    }
    
    // 跳过 exports 定义
    if (i < lines.length && lines[i].includes('Object.defineProperty(exports, "__esModule"')) {
        i++;
        // 跳过到下一个非空行
        while (i < lines.length && (lines[i].trim() === '' || lines[i].includes('value: true') || lines[i].trim() === '});')) {
            i++;
        }
    }
    
    // 处理 exports 声明行
    const exportedFunctions = [];
    if (i < lines.length && lines[i].startsWith('exports.')) {
        const exportsLine = lines[i];
        // 解析导出的内容
        const match = exportsLine.match(/exports\.(.+) = (.+);/);
        if (match) {
            const exportNames = match[1].split(' = exports.');
            exportedFunctions.push(...exportNames);
            restoredLines.push(`// Exports: ${exportNames.join(', ')}`);
        }
        i++;
        // 跳过空行
        while (i < lines.length && lines[i].trim() === '') {
            i++;
        }
    }
    
    // 处理 require 语句，转换为 import
    const imports = [];
    while (i < lines.length && lines[i].includes('require(')) {
        const line = lines[i].trim();
        const match = line.match(/var \$2(.+) = require\("(.+)"\);/);
        if (match) {
            const varName = match[1];
            const modulePath = match[2];
            imports.push(`import { ${varName} } from "./${modulePath}";`);
        } else {
            // 处理其他类型的 require
            const match2 = line.match(/var (.+) = require\("(.+)"\);/);
            if (match2) {
                const varName = match2[1];
                const modulePath = match2[2];
                imports.push(`import ${varName} from "./${modulePath}";`);
            }
        }
        i++;
    }
    
    // 添加 imports
    restoredLines.push(...imports);
    if (imports.length > 0) {
        restoredLines.push('');
    }
    
    // 跳过空行
    while (i < lines.length && lines[i].trim() === '') {
        i++;
    }
    
    // 检查是否是类定义文件
    let isClassFile = false;
    let className = '';
    let baseClass = '';
    
    // 查找类定义
    for (let j = i; j < lines.length; j++) {
        if (lines[j].includes('var def_') && lines[j].includes(' = function')) {
            const match = lines[j].match(/var def_(.+) = function \((.+)\)/);
            if (match) {
                className = match[1];
                baseClass = match[2] || 'BaseSdk';
                isClassFile = true;
                break;
            }
        }
    }
    
    if (isClassFile) {
        // 处理类文件
        restoredLines.push(`export class ${className} extends ${baseClass} {`);
        
        // 查找构造函数
        let foundConstructor = false;
        while (i < lines.length) {
            const line = lines[i];
            
            if (line.includes('function _ctor()') || line.includes('function(')) {
                if (!foundConstructor) {
                    restoredLines.push('    constructor() {');
                    restoredLines.push('        super();');
                    foundConstructor = true;
                }
                // 跳过构造函数定义行
                i++;
                continue;
            }
            
            if (line.includes('.prototype.') && line.includes(' = function')) {
                const match = line.match(/_ctor\.prototype\.(.+) = function\s*\(([^)]*)\)/);
                if (match) {
                    const methodName = match[1];
                    const params = match[2] || '';
                    restoredLines.push(`    ${methodName}(${params}) {`);
                }
            } else if (line.includes('return _ctor;')) {
                restoredLines.push('}');
                break;
            } else if (line.includes('exports.default = def_')) {
                // 跳过，因为我们已经用 export class 了
            } else {
                // 处理普通行，替换 $2 前缀
                let processedLine = line.replace(/\$2(\w+)/g, '$1');
                // 修复一些常见的格式问题
                processedLine = processedLine.replace(/cc__extends\(_ctor, e\);/, '');
                processedLine = processedLine.replace(/var t = null !== e && e\.apply\(this, arguments\) \|\| this;/, '');
                
                if (processedLine.trim() !== '') {
                    restoredLines.push(processedLine);
                }
            }
            
            i++;
        }
    } else {
        // 处理非类文件（如配置文件、工具函数等）
        while (i < lines.length) {
            const line = lines[i];
            
            if (line.startsWith('exports.')) {
                const match = line.match(/exports\.(.+) = (.+);/);
                if (match) {
                    restoredLines.push(`export const ${match[1]} = ${match[2]};`);
                }
            } else {
                // 处理普通行，替换 $2 前缀
                let processedLine = line.replace(/\$2(\w+)/g, '$1');
                restoredLines.push(processedLine);
            }
            
            i++;
        }
    }
    
    return restoredLines.join('\n');
}

// 处理所有文件
let processedCount = 0;
files.forEach(file => {
    try {
        const inputPath = path.join(scriptsDir, file);
        const outputPath = path.join(outputDir, file.replace('.js', '.ts'));
        
        console.log(`Processing: ${file}`);
        const restoredContent = restoreFile(inputPath);
        
        fs.writeFileSync(outputPath, restoredContent, 'utf8');
        processedCount++;
        
        if (processedCount % 10 === 0) {
            console.log(`Processed ${processedCount}/${files.length} files`);
        }
    } catch (error) {
        console.error(`Error processing ${file}:`, error.message);
    }
});

console.log(`\nCompleted! Processed ${processedCount}/${files.length} files`);
console.log(`Output directory: ${outputDir}`);
