// LatticeMap.ts
// 从 LatticeMap.js 转换而来

import { GameSeting } from "./GameSeting";
import { Game } from "./Game";
import { BaseEntity } from "./BaseEntity";

/**
 * 格子地图类
 * 用于管理游戏中的格子地图系统
 */
export class LatticeMap {
    /** 地图宽度 */
    public mapWidth: number = 0;
    
    /** 地图高度 */
    public mapHeight: number = 0;
    
    /** 格子大小 */
    public cellSize: number = 32;
    
    /** 地图数据 */
    private mapData: number[][] = [];
    
    /** 实体列表 */
    private entities: Map<string, BaseEntity> = new Map();
    
    /** 格子实体映射 */
    private cellEntities: Map<string, Set<BaseEntity>> = new Map();
    
    constructor(width: number, height: number, cellSize: number = 32) {
        this.mapWidth = width;
        this.mapHeight = height;
        this.cellSize = cellSize;
        this.initMap();
    }
    
    /**
     * 初始化地图
     */
    private initMap(): void {
        this.mapData = [];
        for (let y = 0; y < this.mapHeight; y++) {
            this.mapData[y] = [];
            for (let x = 0; x < this.mapWidth; x++) {
                this.mapData[y][x] = 0; // 0表示空格子
            }
        }
    }
    
    /**
     * 世界坐标转换为格子坐标
     * @param worldPos 世界坐标
     * @returns 格子坐标
     */
    worldToCell(worldPos: cc.Vec2): cc.Vec2 {
        const cellX = Math.floor(worldPos.x / this.cellSize);
        const cellY = Math.floor(worldPos.y / this.cellSize);
        return cc.v2(cellX, cellY);
    }
    
    /**
     * 格子坐标转换为世界坐标
     * @param cellPos 格子坐标
     * @returns 世界坐标
     */
    cellToWorld(cellPos: cc.Vec2): cc.Vec2 {
        const worldX = cellPos.x * this.cellSize + this.cellSize / 2;
        const worldY = cellPos.y * this.cellSize + this.cellSize / 2;
        return cc.v2(worldX, worldY);
    }
    
    /**
     * 检查格子坐标是否有效
     * @param cellPos 格子坐标
     * @returns 是否有效
     */
    isValidCell(cellPos: cc.Vec2): boolean {
        return cellPos.x >= 0 && cellPos.x < this.mapWidth &&
               cellPos.y >= 0 && cellPos.y < this.mapHeight;
    }
    
    /**
     * 获取格子数据
     * @param cellPos 格子坐标
     * @returns 格子数据
     */
    getCellData(cellPos: cc.Vec2): number {
        if (!this.isValidCell(cellPos)) {
            return -1; // 无效格子
        }
        return this.mapData[cellPos.y][cellPos.x];
    }
    
    /**
     * 设置格子数据
     * @param cellPos 格子坐标
     * @param value 数据值
     */
    setCellData(cellPos: cc.Vec2, value: number): void {
        if (this.isValidCell(cellPos)) {
            this.mapData[cellPos.y][cellPos.x] = value;
        }
    }
    
    /**
     * 检查格子是否可通行
     * @param cellPos 格子坐标
     * @returns 是否可通行
     */
    isWalkable(cellPos: cc.Vec2): boolean {
        const cellData = this.getCellData(cellPos);
        return cellData === 0; // 0表示可通行
    }
    
    /**
     * 添加实体到地图
     * @param entity 实体
     */
    addEntity(entity: BaseEntity): void {
        const entityId = entity.getId();
        this.entities.set(entityId, entity);
        
        // 更新格子实体映射
        this.updateEntityCell(entity);
    }
    
    /**
     * 从地图移除实体
     * @param entity 实体
     */
    removeEntity(entity: BaseEntity): void {
        const entityId = entity.getId();
        this.entities.delete(entityId);
        
        // 从格子实体映射中移除
        this.removeEntityFromCell(entity);
    }
    
    /**
     * 更新实体格子位置
     * @param entity 实体
     */
    updateEntityCell(entity: BaseEntity): void {
        // 先从旧格子中移除
        this.removeEntityFromCell(entity);
        
        // 添加到新格子
        const worldPos = entity.getPosition();
        const cellPos = this.worldToCell(worldPos);
        const cellKey = `${cellPos.x},${cellPos.y}`;
        
        if (!this.cellEntities.has(cellKey)) {
            this.cellEntities.set(cellKey, new Set());
        }
        
        this.cellEntities.get(cellKey).add(entity);
    }
    
    /**
     * 从格子中移除实体
     * @param entity 实体
     */
    private removeEntityFromCell(entity: BaseEntity): void {
        for (const [cellKey, entitySet] of this.cellEntities) {
            if (entitySet.has(entity)) {
                entitySet.delete(entity);
                if (entitySet.size === 0) {
                    this.cellEntities.delete(cellKey);
                }
                break;
            }
        }
    }
    
    /**
     * 获取指定格子中的实体
     * @param cellPos 格子坐标
     * @returns 实体列表
     */
    getEntitiesInCell(cellPos: cc.Vec2): BaseEntity[] {
        const cellKey = `${cellPos.x},${cellPos.y}`;
        const entitySet = this.cellEntities.get(cellKey);
        return entitySet ? Array.from(entitySet) : [];
    }
    
    /**
     * 获取指定范围内的实体
     * @param centerPos 中心位置
     * @param radius 半径（格子数）
     * @returns 实体列表
     */
    getEntitiesInRange(centerPos: cc.Vec2, radius: number): BaseEntity[] {
        const entities: BaseEntity[] = [];
        const centerCell = this.worldToCell(centerPos);
        
        for (let x = centerCell.x - radius; x <= centerCell.x + radius; x++) {
            for (let y = centerCell.y - radius; y <= centerCell.y + radius; y++) {
                const cellPos = cc.v2(x, y);
                if (this.isValidCell(cellPos)) {
                    entities.push(...this.getEntitiesInCell(cellPos));
                }
            }
        }
        
        return entities;
    }
    
    /**
     * 寻路算法（简单的A*实现）
     * @param startPos 起始位置
     * @param endPos 目标位置
     * @returns 路径点列表
     */
    findPath(startPos: cc.Vec2, endPos: cc.Vec2): cc.Vec2[] {
        const startCell = this.worldToCell(startPos);
        const endCell = this.worldToCell(endPos);
        
        // 简单实现：直线路径
        const path: cc.Vec2[] = [];
        const dx = endCell.x - startCell.x;
        const dy = endCell.y - startCell.y;
        const steps = Math.max(Math.abs(dx), Math.abs(dy));
        
        for (let i = 0; i <= steps; i++) {
            const x = startCell.x + (dx * i / steps);
            const y = startCell.y + (dy * i / steps);
            const cellPos = cc.v2(Math.round(x), Math.round(y));
            
            if (this.isValidCell(cellPos) && this.isWalkable(cellPos)) {
                path.push(this.cellToWorld(cellPos));
            }
        }
        
        return path;
    }
    
    /**
     * 清空地图
     */
    clear(): void {
        this.entities.clear();
        this.cellEntities.clear();
        this.initMap();
    }
    
    /**
     * 获取地图信息
     * @returns 地图信息
     */
    getMapInfo(): { width: number; height: number; cellSize: number; entityCount: number } {
        return {
            width: this.mapWidth,
            height: this.mapHeight,
            cellSize: this.cellSize,
            entityCount: this.entities.size
        };
    }
}

export { LatticeMap };
