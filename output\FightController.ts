import { CallID } from "./CallID";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { KnapsackVo } from "./KnapsackVo";
import { NodePool } from "./NodePool";
import FightModel from "./FightModel";
import { Game } from "./Game";

export class FightController extends MVC.MController {
    constructor() {
        super();
        this.setup(FightModel.getInstance);
        this.changeListener(true);
    }

    reset(): void {}

    get classname(): string {
        return "FightController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.Game_Load, this.onOpenGame, this, 200);
        Notifier.changeListener(enable, ListenID.Game_Replay, this.onReplay, this);
        Notifier.changeListener(enable, ListenID.Fight_BackToMain, this.backToMain, this);
        Notifier.changeCall(enable, CallID.Fight_GetCutMode, this.getCutMode, this);
        Notifier.changeCall(enable, CallID.Fight_GetKnapsackMgr, this.getKnapsackMgr, this);
        Notifier.changeListener(enable, ListenID.Fight_End, this.onFight_End, this);
        Notifier.changeListener(enable, ListenID.Fight_Win, this.onFight_Win, this);
    }

    loginFinish(): void {}

    getKnapsackMgr(key: any): any {
        return KnapsackVo.KMap.get(key);
    }

    backToMain(): void {
        this._model.cutMode = Game.Mode.NONE;
        Game.setGameSpeed(1);
        NodePool.clear();
        Manager.restoreGroupMatrix();
    }

    onReplay(): void {
        NodePool.clear();
    }

    onOpenGame(mode: any): void {
        this._model.cutMode = mode;
        UIManager.Close("ui/setting/MoreGamesView");
    }

    getCutMode(): any {
        return this._model.cutMode;
    }

    onFight_End(): void {
        Game.setGameSpeed(1);
        Game.mgr?.sendEvent("gameFail");
    }

    onFight_Win(): void {
        Game.setGameSpeed(1);
        Game.mgr?.sendEvent("gameWin");
    }
}
