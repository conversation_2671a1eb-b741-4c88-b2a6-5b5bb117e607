import { MVC } from "./MVC";
import { GameSeting } from "./GameSeting";

export default class ADModel extends MVC.BaseModel {
    private static _instance: ADModel = null;
    
    public data: GameSeting.TMap<number, number>;
    public isVideoIng: boolean = false;

    constructor() {
        super();
        this.data = new GameSeting.TMap<number, number>();
        this.isVideoIng = false;
        
        if (ADModel._instance == null) {
            ADModel._instance = this;
        }
    }

    reset(): void {}

    static get instance(): ADModel {
        if (ADModel._instance == null) {
            ADModel._instance = new ADModel();
        }
        return ADModel._instance;
    }
}
