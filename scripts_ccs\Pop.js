var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Pop = undefined;
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2UIManager = require("UIManager");
var $2EaseScaleTransition = require("EaseScaleTransition");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var exp_Pop = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.nodeArr = [];
    t.labelArr = [];
    t.UItype = $2GameSeting.GameSeting.TweenType.Game;
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "param", {
    get: function () {
      return this._openArgs.param;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.onOpen = function () {
    this.UItype == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
  };
  _ctor.prototype.setInfo = function () {};
  _ctor.prototype.onShow = function () {};
  _ctor.prototype.onShowFinish = function () {
    this.UItype == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, true);
  };
  _ctor.prototype.onHide = function () {};
  _ctor.prototype.onHideFinish = function () {
    this.UItype == $2GameSeting.GameSeting.TweenType.Game && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_SetPause, false);
  };
  _ctor.prototype.onClose = function () {
    this.unscheduleAllCallbacks();
  };
  _ctor.prototype.changeListener = function () {};
  _ctor.prototype.onClickTTTTTTTTTs = function () {
    $2Notifier.Notifier.send($2ListenID.ListenID.Test_OpenView);
  };
  _ctor.prototype.openView = function (e, t) {
    t.includes("ui/") && $2UIManager.UIManager.Open(t, $2MVC.MVC.openArgs());
  };
  cc__decorate([ccp_property([cc.Node])], _ctor.prototype, "nodeArr", undefined);
  cc__decorate([ccp_property([cc.Label])], _ctor.prototype, "labelArr", undefined);
  cc__decorate([ccp_property({
    type: cc.Enum($2GameSeting.GameSeting.TweenType)
  })], _ctor.prototype, "UItype", undefined);
  return cc__decorate([ccp_ccclass, $2MVC.MVC.transition($2EaseScaleTransition.EaseScaleTransition), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Popup), ccp_menu("ViewComponent/Pop")], _ctor);
}($2MVC.MVC.BaseView);
exports.Pop = exp_Pop;