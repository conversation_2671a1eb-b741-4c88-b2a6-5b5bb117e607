var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MMGuards = undefined;
var $2Cfg = require("Cfg");
var $2Notifier = require("Notifier");
var $2NotifyID = require("NotifyID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Manager = require("Manager");
var $2FPolygonCollider = require("FPolygonCollider");
var $2Game = require("Game");
var $2BronMonsterManger = require("BronMonsterManger");
var $2CompManager = require("CompManager");
var $2NodePool = require("NodePool");
var $2ModeManGuardsModel = require("ModeManGuardsModel");
var $2MMGMonster = require("MMGMonster");
var $2MMGRole = require("MMGRole");
(function (e) {
  var t;
  (function (e) {
    e[e.NONE = 0] = "NONE";
    e[e.Playing = 1] = "Playing";
    e[e.BulletAnim = 2] = "BulletAnim";
    e[e.Determine = 3] = "Determine";
  })(t = e.RoundStatus || (e.RoundStatus = {}));
  var o = function (e) {
    function t(t) {
      var o = e.call(this, t) || this;
      o.cameraZoomRatio = 1;
      o.monsterList = [];
      o.passParam = t;
      return o;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "mode", {
      get: function () {
        return $2ModeManGuardsModel.default.instance;
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.loadMap = function (e, t) {
      var o = this;
      this.gameNode = e;
      this.miniGameCfg = $2Cfg.Cfg.MiniGameLv.get(this.passParam.id);
      this._entityNode = e.getChildByName("entityNode");
      this._bulletNode = e.getChildByName("bulletNode");
      this._mapNode = e.getChildByName("mapNode");
      this._botEffectNode = e.getChildByName("botEffectNode");
      this._topEffectNode = e.getChildByName("topEffectNode");
      this.LifeBarUI = e.getORaddChildByName("LifeBarUI");
      this.topUINode = e.getORaddChildByName("topUINode");
      this.behitUI = e.getORaddChildByName("behitUI");
      this._finishCall = t;
      $2Manager.Manager.setPhysics(true, false);
      this.curBulletList = [];
      $2Manager.Manager.loader.loadPrefab(this.miniGameCfg.lvPrefab).then(function (e) {
        e.setAttribute({
          parent: o._mapNode,
          zIndex: -1
        });
        o.myTiledMap = o._mapNode.getComByChild(cc.TiledMap);
        o.monsterList = [];
        o.createRole(o.myTiledMap.getObjectGroup("born").getObject("player")).then(function () {
          var e = o.myTiledMap.getObjectGroup("born").getObjects();
          for (var t = 0; t < e.length; t++) {
            var i = e[t];
            "player" != i.name && o.createMonster($2Cfg.Cfg.bagMonsterLv.find({
              lv: o.miniGameCfg.lvid
            }), i).then(function (e) {
              e.roleNode.scaleX *= e.position.x > o.mainRole.position.x ? -1 : 1;
            });
          }
        });
        o.physicsPolygonColliders = o.myTiledMap.getComponents(cc.PhysicsPolygonCollider);
        var t = o.myTiledMap.getComponent($2FPolygonCollider.default);
        t.points = [];
        o.physicsPolygonColliders.forEach(function (e) {
          e.enabled && e.points.forEach(function (o) {
            return t.points.push(o.add(e.offset));
          });
        });
      });
      this.gameCamera.setZoomRatio(this.cameraZoomRatio);
      this.gameCamera.setPosition(cc.Vec2.ZERO);
      this.bronMonsterMgr = this.gameNode.getORaddComponent(v);
      this.bronMonsterMgr.init();
      this._finishCall && this._finishCall();
    };
    t.prototype.getObjPos = function (e) {
      return cc.v2(e.x - this.myTiledMap.node.width / 2 + e.width / 2, e.y - this.myTiledMap.node.height / 2 - e.height);
    };
    t.prototype.gameEnd = function () {
      var e = this;
      $2Game.Game.timerOnce(function () {
        e.gameState = $2Game.Game.State.NONE;
      }, .5);
    };
    t.prototype.createRole = function (e) {
      var t = this;
      var o = this.getObjPos(e);
      return new Promise(function (i) {
        $2NodePool.NodePool.spawn("entity/fight/ModeManGuards/role").setNodeAssetFinishCall(function (n) {
          var r = n.getComponent($2MMGRole.default);
          r.node.nodeTag = 999;
          n.parent = t._entityNode;
          r.setPosition(o);
          r.setRotateData({
            startAngle: e.startAngle,
            rotateDirection: e.rotateDiretion
          });
          r.init();
          r.setRole();
          $2CompManager.default.Instance.registerComp(r);
          t.mainRole = r;
          i(r);
        });
      });
    };
    t.prototype.gamePause = function (t) {
      e.prototype.gamePause.call(this, t);
      $2Manager.Manager.setPhysics(!t);
    };
    t.prototype.spawnBullet = function (t, o, i) {
      var n = this;
      undefined === i && (i = {});
      return new Promise(function (r) {
        e.prototype.spawnBullet.call(n, t, o, i).then(function (e) {
          n.curBulletList.push(e);
          r(e);
        });
      });
    };
    t.prototype.createMonster = function (e, t) {
      var o = this;
      var i = this.getObjPos(t);
      return new Promise(function (n) {
        var s = $2Cfg.Cfg.Monster.get(e.monId[0]);
        $2NodePool.NodePool.spawn("entity/fight/ModeManGuards/monster").setNodeAssetFinishCall(function (r) {
          r || console.error("怪物生成错误", null == s ? undefined : s.name);
          var c = r.getComponent($2MMGMonster.default);
          c.node.nodeTag = Number(t.name.split("_")[1]);
          r.parent = o._entityNode;
          c.setPosition(i);
          c.setRotateData({
            startAngle: t.startAngle,
            rotateDirection: t.rotateDiretion
          });
          c.monsterId = s.id;
          c.lvCfg = e;
          c.init();
          o.monsterList.push(c);
          o._monsterMap.set(c.ID, c);
          o.elementMap.set(c.ID, c);
          n(c);
          $2Notifier.Notifier.send($2ListenID.ListenID.Game_LoadFinish);
        });
      });
    };
    Object.defineProperty(t.prototype, "mainRole", {
      get: function () {
        return this._mainRole;
      },
      set: function (e) {
        this._mainRole = e;
      },
      enumerable: false,
      configurable: true
    });
    return t;
  }($2Game.Game.Mgr);
  e.Mgr = o;
  cc.v2();
  e.maxPower = 200;
  var i = function (e) {
    function t() {
      var t = e.call(this) || this;
      t.fireNum = 0;
      return t;
    }
    cc__extends(t, e);
    Object.defineProperty(t.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(t.prototype, "aimData", {
      get: function () {
        return this._aimData;
      },
      set: function (e) {
        this._aimData = e;
        (this.ower instanceof $2MMGRole.default || this.ower instanceof $2MMGMonster.default) && this.ower.setLine(e.angle);
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.fire = function (e) {
      var t = this;
      undefined === e && (e = .5);
      (this.ower instanceof $2MMGRole.default || this.ower instanceof $2MMGMonster.default) && this.ower.hideLine();
      $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick, true);
      this.ower.delayByGame(function () {
        t.ower.firstSkill.checkTarget();
        $2Notifier.Notifier.send($2NotifyID.NotifyID.Game_BanClick, false);
      }, e);
      this.fireNum++;
      this.ower.mySkeleton.playQueue(["attack", "idle"], true);
    };
    return t;
  }($2GameSeting.GameSeting.CompBase);
  e.KnifeController = i;
  var v = function (e) {
    function o() {
      var o = null !== e && e.apply(this, arguments) || this;
      o._batchNum = 0;
      o.buffOffset = 0;
      o.cutStaus = t.NONE;
      o.countDown = 0;
      return o;
    }
    cc__extends(o, e);
    Object.defineProperty(o.prototype, "batchNum", {
      get: function () {
        return this._batchNum;
      },
      set: function (e) {
        this._batchNum = e;
        0 != e && $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRound, e);
      },
      enumerable: false,
      configurable: true
    });
    Object.defineProperty(o.prototype, "game", {
      get: function () {
        return $2Game.Game.mgr;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.init = function () {};
    Object.defineProperty(o.prototype, "curBullet", {
      get: function () {
        return this.game.curBulletList;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.onUpdate = function (e) {
      if (this.game.gameState == $2Game.Game.State.START && ((this._dtTime += e) >= 1.5 && (this._dtTime = 0), 0 == this._dtTime && this.cutStaus == t.BulletAnim)) {
        var o = true;
        this.curBullet.forEach(function (e) {
          !e.isActive || e.isDead || (o = false);
        });
        o && this.changeGameStatus(t.Determine);
      }
    };
    Object.defineProperty(o.prototype, "role", {
      get: function () {
        return this.game.mainRole;
      },
      enumerable: false,
      configurable: true
    });
    o.prototype.changeListener = function (t) {
      e.prototype.changeListener.call(this, t);
    };
    o.prototype.changeGameStatus = function (e) {
      var o = this;
      switch (e) {
        case t.NONE:
          this.cutRoundType = e;
          break;
        case t.Playing:
          break;
        case t.Determine:
          this.scheduleOnce(function () {
            var e = false;
            o.game.monsterList.forEach(function (t) {
              t.isDead || (e = true);
            });
            if (o.role.isDead || e) {
              o.game.sendEvent("BatchFail");
              $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, false);
            } else {
              $2Notifier.Notifier.send($2ListenID.ListenID.Fight_End, true);
            }
          }, 1);
          break;
        case t.BulletAnim:
      }
      $2Notifier.Notifier.send($2ListenID.ListenID.Fight_GameRoundType, e);
      this.cutStaus = e;
    };
    return o;
  }($2BronMonsterManger.BronMonsterManger);
  e.SpawningMgr = v;
})(exports.MMGuards || (exports.MMGuards = {}));