import { CallID } from "./CallID";
import { Cfg } from "./Cfg";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { GameUtil } from "./GameUtil";
import { AlertManager } from "./AlertManager";
import { BaseEntity } from "./BaseEntity";
import { GameEffect } from "./GameEffect";
import { Goods } from "./Goods";
import { GameCamera } from "./GameCamera";
import CompManager from "./CompManager";
import { NodePool } from "./NodePool";
import { BronMonsterManger } from "./BronMonsterManger";
import FColliderManager from "./FColliderManager";
import { PropertyVo } from "./PropertyVo";
import { Pet } from "./Pet";
import { MVC } from "./MVC";
import { LatticeMap } from "./LatticeMap";
import { LifeBar } from "./LifeBar";
import { CurrencyConfigDefine } from "./CurrencyConfigCfg";
import { BulletBase } from "./BulletBase";
import { GameSeting } from "./GameSeting";
import { GameSkeleton } from "./GameSkeleton";
import { Time } from "./Time";
import { LifeLabel } from "./LifeLabel";

export namespace Game {
    export enum State {
        NONE = 0,
        START = 1,
        PAUSE = 2
    }

    export enum Mode {
        NONE = 0,
        ROUGUELIKE = 1,
        TOWER = 2,
        CATGAME = 3,
        HIDEGAME = 4,
        MOYUTOWER = 5,
        BACKPACKHERO = 20,
        THROWINGKNIFE = 30,
        PICKUPBULLETS = 31,
        BULLETSREBOUND = 32,
        CHAINS = 33,
        TIDEDEFEND = 34,
        MANGUARDS = 35,
        ALLOUTATTACK = 36,
        DRAGONWAR = 37
    }

    export const ModeMouth: { [key: number]: any } = {
        [Mode.NONE]: {
            mouth: ListenID.Game_Load,
            get data() {
                return MVC.openArgs();
            }
        },
        [Mode.BACKPACKHERO]: {
            mouth: ListenID.Game_Load,
            get data() {
                return MVC.openArgs();
            }
        },
        [Mode.THROWINGKNIFE]: {
            name: "飞刀模式",
            icon: "img/ModeTKnife/icon_sjxyx",
            mouth: ListenID.Game_Load,
            get data() {
                return MVC.openArgs();
            }
        },
        [Mode.PICKUPBULLETS]: {
            name: "让子弹飞",
            icon: "img/ModePickupBullet/icon_zbzd",
            mouth: ListenID.Game_Load,
            get data() {
                return MVC.openArgs();
            }
        },
        [Mode.BULLETSREBOUND]: {
            name: "弹来弹去",
            icon: "img/ModeBulletsRebound/icon_tltq",
            mouth: ListenID.Game_Load,
            get data() {
                return MVC.openArgs();
            }
        },
        [Mode.CHAINS]: {
            name: "末日屠龙",
            icon: "v1/images/fight/icon_tcl",
            mouth: ListenID.Game_Load,
            get data() {
                return MVC.openArgs();
            }
        },
        [Mode.TIDEDEFEND]: {
            name: "尸潮防线",
            icon: "img/ModeTideDefend/icon_mtwf",
            mouth: ListenID.Game_Load,
            get data() {
                return MVC.openArgs();
            }
        }
    };

    export class GameManager {
        public static mgr: GameManager | null = null;
        public state: State = State.NONE;
        public mode: Mode = Mode.NONE;
        public gameSpeed: number = 1;
        public isPause: boolean = false;
        public gameTime: number = 0;
        public deltaTime: number = 0;
        public frameCount: number = 0;

        private _entities: BaseEntity[] = [];
        private _camera: GameCamera | null = null;
        private _colliderManager: FColliderManager | null = null;

        constructor() {
            GameManager.mgr = this;
            this.init();
        }

        init(): void {
            this._colliderManager = FColliderManager.instance;
            this.setupListeners();
        }

        private setupListeners(): void {
            Notifier.changeListener(true, ListenID.Game_Start, this.onGameStart, this);
            Notifier.changeListener(true, ListenID.Game_Pause, this.onGamePause, this);
            Notifier.changeListener(true, ListenID.Game_Resume, this.onGameResume, this);
            Notifier.changeListener(true, ListenID.Game_End, this.onGameEnd, this);
        }

        onGameStart(): void {
            this.state = State.START;
            this.isPause = false;
            this.gameTime = 0;
            this.frameCount = 0;
        }

        onGamePause(): void {
            this.state = State.PAUSE;
            this.isPause = true;
        }

        onGameResume(): void {
            this.state = State.START;
            this.isPause = false;
        }

        onGameEnd(): void {
            this.state = State.NONE;
            this.isPause = false;
            this.clearEntities();
        }

        update(dt: number): void {
            if (this.isPause || this.state !== State.START) {
                return;
            }

            this.deltaTime = dt * this.gameSpeed;
            this.gameTime += this.deltaTime;
            this.frameCount++;

            // Update entities
            for (let i = this._entities.length - 1; i >= 0; i--) {
                const entity = this._entities[i];
                if (entity && entity.isValid) {
                    entity.update(this.deltaTime);
                } else {
                    this._entities.splice(i, 1);
                }
            }

            // Update component manager
            CompManager.Instance.onUpdate(this.deltaTime);

            // Update collision manager
            if (this._colliderManager) {
                this._colliderManager.update(this.deltaTime);
            }
        }

        addEntity(entity: BaseEntity): void {
            if (entity && this._entities.indexOf(entity) === -1) {
                this._entities.push(entity);
            }
        }

        removeEntity(entity: BaseEntity): void {
            const index = this._entities.indexOf(entity);
            if (index !== -1) {
                this._entities.splice(index, 1);
            }
        }

        clearEntities(): void {
            this._entities.length = 0;
        }

        setGameSpeed(speed: number): void {
            this.gameSpeed = Math.max(0, speed);
        }

        getGameSpeed(): number {
            return this.gameSpeed;
        }

        sendEvent(eventName: string, data?: any): void {
            Notifier.send(ListenID.Event_SendEvent, eventName, data);
        }

        restoreGroupMatrix(): void {
            // Restore collision group matrix
            Manager.restoreGroupMatrix();
        }

        getCamera(): GameCamera | null {
            return this._camera;
        }

        setCamera(camera: GameCamera): void {
            this._camera = camera;
        }

        getColliderManager(): FColliderManager | null {
            return this._colliderManager;
        }

        // Static methods for global access
        static setGameSpeed(speed: number): void {
            if (GameManager.mgr) {
                GameManager.mgr.setGameSpeed(speed);
            }
        }

        static getGameSpeed(): number {
            return GameManager.mgr ? GameManager.mgr.getGameSpeed() : 1;
        }

        static sendEvent(eventName: string, data?: any): void {
            if (GameManager.mgr) {
                GameManager.mgr.sendEvent(eventName, data);
            }
        }

        static restoreGroupMatrix(): void {
            if (GameManager.mgr) {
                GameManager.mgr.restoreGroupMatrix();
            }
        }
    }

    // Initialize game manager
    export const mgr = new GameManager();
}

export const ModeCfg = Game.ModeMouth;
