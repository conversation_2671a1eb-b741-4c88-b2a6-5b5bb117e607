var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.languageCfgReader = exports.languageDefine = undefined;
var $2TConfig = require("TConfig");
exports.languageDefine || (exports.languageDefine = {});
var exp_languageCfgReader = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._name = "language";
    return t;
  }
  cc__extends(_ctor, e);
  return _ctor;
}($2TConfig.TConfig);
exports.languageCfgReader = exp_languageCfgReader;