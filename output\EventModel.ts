import { MVC } from "./MVC";

export default class EventModel extends MVC.BaseModel {
    private static _instance: EventModel | null = null;
    public userVo: any = {};

    constructor() {
        super();
        if (EventModel._instance == null) {
            EventModel._instance = this;
        }
    }

    reset(): void {}

    static get instance(): EventModel {
        if (EventModel._instance == null) {
            EventModel._instance = new EventModel();
        }
        return EventModel._instance;
    }
}
