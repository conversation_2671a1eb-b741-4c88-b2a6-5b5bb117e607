// JUHEAndroid.ts
// 从 JUHEAndroid.js 转换而来

import { BaseSdk, VideoAdCode } from "./BaseSdk";

// 扩展cc命名空间
declare global {
    namespace cc {
        let nativeAndroid: any;
    }
    
    interface Window {
        jsb: any;
    }
}

/**
 * JUHE Android SDK类
 * 处理Android平台的聚合SDK功能
 */
export default class JUHEAndroid extends BaseSdk {
    /** 默认Java类名 */
    private defaultClass: string = "org/cocos2dx/javascript/AppActivity";
    
    /** Banner显示回调 */
    public onbannerShow: Function = () => {};
    
    /** 是否获得奖励 */
    private isGetReward: boolean = false;
    
    /** 用户名 */
    private _uname: string = "";
    
    /** 令牌 */
    private _token: string = "";
    
    /** OAID */
    private _oaid: string = "";
    
    /** 登录成功回调 */
    private _loginSuccess: Function = null;
    
    /** 登录失败回调 */
    private _loginfail: Function = null;
    
    /** Banner显示次数 */
    public showbannerNum: number = 0;
    
    /** 原生调用方法 */
    private callStaticMethod: Function;
    
    constructor() {
        super();
        
        // 初始化原生Android对象
        cc.nativeAndroid = cc.nativeAndroid || {};
        
        // 设置原生调用方法
        this.callStaticMethod = (window.jsb && window.jsb.reflection) 
            ? window.jsb.reflection.callStaticMethod 
            : () => {};
    }
    
    /**
     * 登出
     */
    logout(): void {
        this.callStaticMethod(this.defaultClass, "logout", "()V");
    }
    
    /**
     * 初始化SDK
     * @param config 配置参数
     */
    init(config: any): void {
        super.init(config);
        
        const self = this;
        
        // 设置广告回调
        const adCallbacks = {
            // 激励视频完成
            onRewardVideoComplete: () => {
                self.isGetReward = true;
            },
            
            // 激励视频关闭
            onRewardVideoClose: () => {
                if (self.isGetReward) {
                    self.onPlayEnd && self.onPlayEnd(VideoAdCode.COMPLETE, "看完广告");
                } else {
                    self.onPlayEnd && self.onPlayEnd(VideoAdCode.NOT_COMPLITE, "未完整观看广告");
                }
            },
            
            // 激励视频显示
            onRewardVideoShow: () => {
                self.onPlayEnd && self.onPlayEnd(VideoAdCode.SHOW_SUCCESS, "");
            },
            
            // 激励视频失败
            onRewardVideoFail: () => {
                self.onPlayEnd && self.onPlayEnd(VideoAdCode.AD_ERROR, "内容正在加载中，请稍后再试");
            },
            
            // 插屏广告显示
            onInterstitialShow: () => {
                console.log("onInterstitialShow");
            },
            
            // 插屏广告关闭
            onInterstitialClose: () => {
                console.log("onInterstitialClose");
            },
            
            // 插屏广告失败
            onInterstitialFail: () => {
                console.log("onInterstitialFail");
            },
            
            // Banner广告显示
            onBannerShow: () => {
                self.onbannerShow();
                self.showbannerNum++;
            },
            
            // Banner广告关闭
            onBannerClose: () => {
                console.log("onBannerClose");
            },
            
            // Banner广告失败
            onBannerFail: () => {
                console.log("onBannerFail");
            }
        };
        
        // 注册回调到原生
        this.registerCallbacks(adCallbacks);
    }
    
    /**
     * 注册回调函数到原生
     * @param callbacks 回调函数对象
     */
    private registerCallbacks(callbacks: any): void {
        for (const key in callbacks) {
            if (callbacks.hasOwnProperty(key)) {
                cc.nativeAndroid[key] = callbacks[key];
            }
        }
    }
    
    /**
     * 显示激励视频
     * @param adId 广告ID
     */
    showRewardVideo(adId?: string): void {
        this.isGetReward = false;
        this.callStaticMethod(this.defaultClass, "showRewardVideo", "(Ljava/lang/String;)V", adId || "");
    }
    
    /**
     * 显示插屏广告
     * @param adId 广告ID
     */
    showInterstitial(adId?: string): void {
        this.callStaticMethod(this.defaultClass, "showInterstitial", "(Ljava/lang/String;)V", adId || "");
    }
    
    /**
     * 显示Banner广告
     * @param adId 广告ID
     */
    showBanner(adId?: string): void {
        this.callStaticMethod(this.defaultClass, "showBanner", "(Ljava/lang/String;)V", adId || "");
    }
    
    /**
     * 隐藏Banner广告
     */
    hideBanner(): void {
        this.callStaticMethod(this.defaultClass, "hideBanner", "()V");
    }
    
    /**
     * 登录
     * @param successCallback 成功回调
     * @param failCallback 失败回调
     */
    login(successCallback?: Function, failCallback?: Function): void {
        this._loginSuccess = successCallback;
        this._loginfail = failCallback;
        this.callStaticMethod(this.defaultClass, "login", "()V");
    }
    
    /**
     * 获取用户信息
     * @returns 用户信息
     */
    getUserInfo(): { uname: string; token: string; oaid: string } {
        return {
            uname: this._uname,
            token: this._token,
            oaid: this._oaid
        };
    }
    
    /**
     * 设置用户信息
     * @param uname 用户名
     * @param token 令牌
     * @param oaid OAID
     */
    setUserInfo(uname: string, token: string, oaid: string): void {
        this._uname = uname;
        this._token = token;
        this._oaid = oaid;
    }
    
    /**
     * 分享
     * @param title 标题
     * @param content 内容
     * @param imageUrl 图片URL
     */
    share(title: string, content: string, imageUrl?: string): void {
        this.callStaticMethod(
            this.defaultClass, 
            "share", 
            "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", 
            title, 
            content, 
            imageUrl || ""
        );
    }
    
    /**
     * 退出游戏
     */
    exitGame(): void {
        this.callStaticMethod(this.defaultClass, "exitGame", "()V");
    }
    
    /**
     * 获取设备信息
     * @returns 设备信息
     */
    getDeviceInfo(): any {
        return this.callStaticMethod(this.defaultClass, "getDeviceInfo", "()Ljava/lang/String;");
    }
    
    /**
     * 检查是否支持广告
     * @param adType 广告类型
     * @returns 是否支持
     */
    isAdSupported(adType: string): boolean {
        return this.callStaticMethod(
            this.defaultClass, 
            "isAdSupported", 
            "(Ljava/lang/String;)Z", 
            adType
        );
    }
}
