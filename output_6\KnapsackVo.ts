// KnapsackVo.ts
// 从 KnapsackVo.js 转换而来

import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { CurrencyConfigCfg } from "./CurrencyConfigCfg";
import { Notifier } from "./Notifier";
import { Manager } from "./Manager";
import { Time } from "./Time";

/**
 * 背包数据项接口
 */
interface KnapsackItem {
    num: number;
    expireTime?: number;
    [key: string]: any;
}

/**
 * 背包数据类
 */
class KnapsackData {
    /** 数据名称 */
    public name: string = "KnapsackData";
    
    /** 是否自动保存 */
    public isAutoSave: boolean = true;
    
    /** 是否发送消息 */
    public isSendMsg: boolean = true;
    
    /** 数据列表 */
    private list: { [key: string]: KnapsackItem } = {};
    
    /**
     * 构造函数
     * @param autoSave 是否自动保存，默认true
     * @param name 数据名称
     */
    constructor(autoSave: boolean = true, name?: string) {
        this.isAutoSave = autoSave;
        this.name = name || "KnapsackData";
        
        if (this.isAutoSave) {
            const savedData = Manager.storage.getString(this.name) || "{}";
            this.list = JSON.parse(savedData);
            this.SaveData();
        } else {
            this.list = {};
        }
        
        // 注册到全局Map
        KnapsackVo.KMap.set(this.name, this);
    }
    
    /**
     * 检查是否拥有指定数量的物品
     * @param itemId 物品ID
     * @returns 拥有的数量
     */
    has(itemId: string | number): number {
        if (this.checkExpire(itemId)) {
            return 0;
        }
        
        const item = this.list[itemId];
        return item ? item.num : 0;
    }
    
    /**
     * 获取物品数据
     * @param itemId 物品ID
     * @returns 物品数据
     */
    getItem(itemId: string | number): KnapsackItem | null {
        if (this.checkExpire(itemId)) {
            return null;
        }
        
        return this.list[itemId] || null;
    }
    
    /**
     * 获取物品值
     * @param itemId 物品ID
     * @param key 属性键，默认为"num"
     * @returns 属性值
     */
    getVal(itemId: string | number, key: string = "num"): any {
        const item = this.getItem(itemId);
        return item ? item[key] : 0;
    }
    
    /**
     * 设置物品数据
     * @param itemId 物品ID
     * @param value 值或物品数据对象
     * @param key 属性键，默认为"num"
     */
    set(itemId: string | number, value: any, key: string = "num"): void {
        if (!this.list[itemId]) {
            this.list[itemId] = { num: 0 };
        }
        
        if (typeof value === "object") {
            this.list[itemId] = { ...this.list[itemId], ...value };
        } else {
            this.list[itemId][key] = value;
        }
        
        this.SaveData();
        this.sendChangeNotification(itemId);
    }
    
    /**
     * 添加物品
     * @param itemId 物品ID
     * @param num 数量
     * @param expireTime 过期时间（可选）
     */
    add(itemId: string | number, num: number, expireTime?: number): void {
        if (!this.list[itemId]) {
            this.list[itemId] = { num: 0 };
        }
        
        this.list[itemId].num += num;
        
        if (expireTime !== undefined) {
            this.list[itemId].expireTime = Time.time + expireTime;
        }
        
        this.SaveData();
        this.sendChangeNotification(itemId);
    }
    
    /**
     * 减少物品
     * @param itemId 物品ID
     * @param num 数量
     * @returns 是否成功减少
     */
    reduce(itemId: string | number, num: number): boolean {
        const currentNum = this.has(itemId);
        if (currentNum >= num) {
            this.list[itemId].num -= num;
            
            // 如果数量为0，删除该项
            if (this.list[itemId].num <= 0) {
                delete this.list[itemId];
            }
            
            this.SaveData();
            this.sendChangeNotification(itemId);
            return true;
        }
        
        return false;
    }
    
    /**
     * 删除物品
     * @param itemId 物品ID
     */
    remove(itemId: string | number): void {
        if (this.list[itemId]) {
            delete this.list[itemId];
            this.SaveData();
            this.sendChangeNotification(itemId);
        }
    }
    
    /**
     * 检查物品是否过期
     * @param itemId 物品ID
     * @returns 是否过期
     */
    private checkExpire(itemId: string | number): boolean {
        const item = this.list[itemId];
        if (item && item.expireTime && Time.time > item.expireTime) {
            delete this.list[itemId];
            this.SaveData();
            return true;
        }
        return false;
    }
    
    /**
     * 保存数据
     */
    SaveData(): void {
        if (this.isAutoSave) {
            Manager.storage.setString(this.name, JSON.stringify(this.list));
        }
    }
    
    /**
     * 发送变化通知
     * @param itemId 物品ID
     */
    private sendChangeNotification(itemId: string | number): void {
        if (this.isSendMsg) {
            Notifier.send(ListenID.KNAPSACK_CHANGE, {
                dataName: this.name,
                itemId: itemId,
                newValue: this.has(itemId)
            });
        }
    }
    
    /**
     * 获取所有物品
     * @returns 所有物品数据
     */
    getAllItems(): { [key: string]: KnapsackItem } {
        // 清理过期物品
        const currentTime = Time.time;
        for (const itemId in this.list) {
            const item = this.list[itemId];
            if (item.expireTime && currentTime > item.expireTime) {
                delete this.list[itemId];
            }
        }
        
        this.SaveData();
        return { ...this.list };
    }
    
    /**
     * 清空所有数据
     */
    clear(): void {
        this.list = {};
        this.SaveData();
        
        if (this.isSendMsg) {
            Notifier.send(ListenID.KNAPSACK_CLEAR, { dataName: this.name });
        }
    }
}

/**
 * 背包管理器
 */
export class KnapsackVo {
    /** 全局背包数据Map */
    public static KMap: Map<string, KnapsackData> = new Map();
    
    /**
     * 获取背包数据
     * @param name 数据名称
     * @returns 背包数据实例
     */
    public static getData(name: string): KnapsackData | undefined {
        return KnapsackVo.KMap.get(name);
    }
    
    /**
     * 创建背包数据
     * @param name 数据名称
     * @param autoSave 是否自动保存
     * @returns 背包数据实例
     */
    public static createData(name: string, autoSave: boolean = true): KnapsackData {
        return new KnapsackData(autoSave, name);
    }
    
    /**
     * 删除背包数据
     * @param name 数据名称
     */
    public static removeData(name: string): void {
        const data = KnapsackVo.KMap.get(name);
        if (data) {
            data.clear();
            KnapsackVo.KMap.delete(name);
        }
    }
}

export { KnapsackData };
