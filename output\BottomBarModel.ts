import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { Game } from "./Game";

export default class BottomBarModel extends MVC.BaseModel {
    private static _instance: BottomBarModel = null;

    constructor() {
        super();
        if (BottomBarModel._instance == null) {
            BottomBarModel._instance = this;
        }
    }

    reset(): void {}

    static get instance(): BottomBarModel {
        if (BottomBarModel._instance == null) {
            BottomBarModel._instance = new BottomBarModel();
        }
        return BottomBarModel._instance;
    }

    startGame(gameId: number): void {
        const gameMode = Game.getMouth(gameId);
        if (gameMode.isUnlock) {
            Notifier.send(gameMode.mouth, gameId, gameMode.data);
        }
    }
}
