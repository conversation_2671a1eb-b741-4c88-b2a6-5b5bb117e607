// GameCamera.ts
// 从 GameCamera.js 转换而来

import { GameSeting } from "./GameSeting";
import { Manager } from "./Manager";
import { Time } from "./Time";
import { Game } from "./Game";

/**
 * 游戏摄像机类
 * 负责摄像机的移动、缩放、震动等功能
 */
export class GameCamera extends GameSeting.CompBase {
    /** 目标位置 */
    public targetPos: cc.Vec2 = cc.Vec2.ZERO;
    /** 偏移量 */
    public offset: number = 0;
    /** 临时偏移量 */
    public tempOffset: number = 0;
    /** 缩放X */
    public zoomScaleX: number = 0;
    /** 缩放Y */
    public zoomScaleY: number = 0;
    /** 目标缩放 */
    public targetZoom: number = 0.7;
    /** 比例 */
    public radio: number = 0;
    /** 是否开始震动 */
    private _startShake: boolean = false;
    /** 游戏实例 */
    private _game: any = null;
    /** 设计宽度 */
    private _designWidth: number = 0;
    /** 设计高度 */
    private _designHeight: number = 0;
    /** 摄像机包围盒 */
    public cameraBox: cc.Rect = null;
    /** 临时包围盒 */
    public tempBox: cc.Rect = cc.rect();
    /** 观察位置 */
    public lookPos: cc.Vec2 = cc.v2();
    /** 偏移向量 */
    private _offset: cc.Vec2 = cc.v2(0, 0);
    /** 方向 */
    public dir: number = 0;
    /** 切换缩放比例 */
    public cutZoomRatio: number = 0.9;
    /** 是否开始缩放 */
    public startzoom: boolean = false;
    /** 摄像机缓动 */
    public cameraTween: any = null;
    /** 摄像机组件 */
    public camera: cc.Camera;
    /** 目标节点 */
    private _targetNode: cc.Node;
    /** 移动限制X */
    public moveLimtX: number[];
    /** 移动限制Y */
    public moveLimtY: number[];

    /**
     * 构造函数
     * @param camera 摄像机组件
     * @param targetNode 目标节点
     * @param game 游戏实例
     */
    constructor(camera: cc.Camera, targetNode: cc.Node, game: any) {
        super();
        
        this.cameraBox = new cc.Rect();
        this.camera = camera;
        this._targetNode = targetNode;
        this._game = game;
        this.tempOffset = this.offset;
        
        const designSize = Manager.vo.designSize;
        this._designHeight = designSize.height;
        this._designWidth = designSize.width;
        this.cameraBox.width = designSize.width;
        this.cameraBox.height = designSize.height;
    }

    /**
     * 获取摄像机位置
     */
    get position(): cc.Vec3 {
        return this.camera.node.position;
    }

    /**
     * 设置目标节点
     * @param targetNode 目标节点
     */
    setTargetNode(targetNode: cc.Node): void {
        this._targetNode = targetNode;
        this.cutZoomRatio = this.camera.zoomRatio;
    }

    /**
     * 延迟更新
     */
    lateUpdate(): void {
        if (this._targetNode) {
            this.lookPos.set(this._targetNode.position);
        }
        
        this.targetPos.x = cc.misc.lerp(this.targetPos.x, this.lookPos.x, Time.deltaTime + 0.06);
        this.targetPos.y = cc.misc.lerp(this.targetPos.y, this.lookPos.y, Time.deltaTime + 0.06);
        
        if (this.moveLimtX) {
            this.targetPos.x = cc.misc.clampf(this.targetPos.x, this.moveLimtX[0], this.moveLimtX[1]);
        }
        if (this.moveLimtY) {
            this.targetPos.y = cc.misc.clampf(this.targetPos.y, this.moveLimtY[0], this.moveLimtY[1]);
        }
        
        if (this._startShake) {
            this.targetPos.addSelf(this._offset);
        }
        
        this.camera.node.position = this.targetPos;
        this.cameraBox.x = this.targetPos.x - 0.5 * this.cameraBox.width;
        this.cameraBox.y = this.targetPos.y - 0.5 * this.cameraBox.height;
    }

    /**
     * 设置缩放比例
     * @param ratio 缩放比例
     * @param duration 持续时间，默认0.3秒
     */
    setZoomRatio(ratio: number, duration: number = 0.3): void {
        if (Math.abs(ratio - this.cutZoomRatio) < 0.01) {
            return;
        }
        
        cc.tween(this.camera)
            .stopLast()
            .to(duration, { zoomRatio: ratio })
            .start();
        
        this.cutZoomRatio = ratio;
    }

    /**
     * 设置移动限制
     * @param limitX X轴限制范围
     * @param limitY Y轴限制范围
     */
    setMoveLimt(limitX: number[], limitY: number[]): void {
        this.moveLimtX = limitX;
        this.moveLimtY = limitY;
    }

    /**
     * 检查是否在摄像机视野内
     * @param left 左边界
     * @param bottom 下边界
     * @param right 右边界
     * @param top 上边界
     * @returns 是否在视野内
     */
    isInCamera(left: number, bottom: number, right: number, top: number): boolean {
        this.tempBox.width = right - left;
        this.tempBox.height = top - bottom;
        this.tempBox.x = left;
        this.tempBox.y = bottom;
        return this.cameraBox.intersects(this.tempBox);
    }

    /**
     * 开始震动
     * @param intensity 震动强度，默认1
     * @param repeatCount 重复次数，默认1
     */
    startShake(intensity: number = 1, repeatCount: number = 1): void {
        this._startShake = true;
        this._offset = cc.Vec2.ZERO;
        
        const shakeIntensity = intensity;
        cc.Tween.stopAllByTarget(this.camera.node.parent);
        
        this.cameraTween = Game.tween(this.camera.node.parent);
        this.cameraTween
            .to(0.03, { position: cc.v2(0 + 8 * shakeIntensity, 0 + 2 * shakeIntensity) })
            .to(0.03, { position: cc.v2(0 - 3 * shakeIntensity, 0 - 8 * shakeIntensity) })
            .to(0.03, { position: cc.v2(0 + 2 * shakeIntensity, 0 + 3 * shakeIntensity) })
            .union()
            .repeat(repeatCount);
        
        this.cameraTween
            .to(0.03, { x: 0, y: 0 })
            .call(() => {
                this._startShake = false;
            })
            .start();
    }

    /**
     * 设置摄像机位置
     * @param position 位置
     */
    setPosition(position: cc.Vec3): void {
        this.camera.node.position.set(position);
    }
}