var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.MCDragon = undefined;
var $2Cfg = require("Cfg");
var $2CurrencyConfigCfg = require("CurrencyConfigCfg");
var $2GameatrCfg = require("GameatrCfg");
var $2GameSeting = require("GameSeting");
var $2Manager = require("Manager");
var $2GameUtil = require("GameUtil");
var $2Dragon = require("Dragon");
var $2Game = require("Game");
var $2PropertyVo = require("PropertyVo");
var $2ModeChainsModel = require("ModeChainsModel");
var $2MChains = require("MChains");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
cc__decorator.property;
cc__decorator.menu;
var exp_MCDragon = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "game", {
    get: function () {
      return $2Game.Game.mgr;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(_ctor.prototype, "mode", {
    get: function () {
      return $2ModeChainsModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function (t, o) {
    var i;
    var n;
    var r = this;
    this.roundMonster = $2GameUtil.GameUtil.deepCopy($2Game.ModeCfg.MonsterLv.filter({
      lv: this.game.pathData.dragonLvId,
      round: t
    }));
    var c = this.game.miniGameCfg.diff || [1, 1, 1];
    var f = $2Manager.Manager.vo.switchVo.dragonDiff.filter(function (e) {
      return e[0] == r.game.pathData.dragonLvId;
    });
    this.roundMonster.forEach(function (e, t) {
      e.hp *= c[0];
      e.atk *= c[1];
      e.speed *= c[2];
      e.hp *= r.game.cutDiffVal.hp;
      e.speed *= r.game.cutDiffVal.speed;
      var o = f.find(function (e) {
        return t >= e[1] && t < e[2];
      });
      o && (e.hp *= 1 + o[3]);
      if (e.dropExpRatio) {
        var i = e.dropExpRatio[0][0];
        if (i == $2CurrencyConfigCfg.CurrencyConfigDefine.buffDrop) {
          var n = $2GameUtil.GameUtil.getRandomByWeightInArray($2Cfg.Cfg.PoolList.get(e.dropExpRatio[0][3]).Pool, 1)[0];
          e.dropExpRatio[0][3] = n;
        }
        i == $2CurrencyConfigCfg.CurrencyConfigDefine.sbuffSelect && r.mode.rVo.selectDiffType != $2GameSeting.GameSeting.DiffType.Hell && (e.dropExpRatio = null);
      }
    });
    var g = this.roundMonster[0];
    this.property = new $2PropertyVo.Property.Vo(this, {
      speed: g.speed,
      atk: g.atk,
      hp: g.hp
    });
    this.rageSpeed = this.game.pathData.otherValue[0][2] || .5;
    this.maxSpeed = g.speed;
    this.monCfg = $2Cfg.Cfg.Monster.get($2GameUtil.GameUtil.randomArr(g.monId));
    e.prototype.setInfo.call(this, t, o);
    this.setAmClip();
    this.isAngleHade = this.monCfg.spine.includes("_f");
    null === (i = this.monCfg.buff) || undefined === i || i.forEach(function (e) {
      return r.addBuff(e);
    });
    null === (n = this.monCfg.skill) || undefined === n || n.forEach(function (e) {
      return r.addSkill(e);
    });
    g.isSpMon && this.preloadDragonToPath(g.isSpMon);
    this.game.totalLen += this.totalLen;
  };
  _ctor.prototype.setAmClip = function () {
    var e = this;
    $2Manager.Manager.loader.loadSpineNode(this.monCfg.spine, {
      nodeAttr: {
        parent: this.node,
        position: cc.Vec2.ZERO
      },
      spAttr: {
        loop: true,
        defaultAnim: "idle",
        isPlayerOnLoad: true
      }
    }).then(function (t) {
      var o;
      e.mySkeleton = t;
      e.mySkeleton.premultipliedAlpha = false;
      e.roleNode = t.node;
      ((null === (o = e.buffMgr) || undefined === o ? undefined : o.attrMapAll.getor($2GameatrCfg.GameatrDefine.movespeed, 0)) || 0) > 0 && e.playAction("anger", true);
    });
  };
  _ctor.prototype.createBody = function (t, o) {
    var i = this;
    return new Promise(function (n) {
      t.hp > 1 && (t.hp = Math.max(1, Math.round(t.hp * (1 + i.mode.gmDiff))));
      e.prototype.createBody.call(i, t, o).then(function (e) {
        n(e);
      });
    });
  };
  _ctor.prototype.toDead = function () {
    this.bodyList.forReverse(function (e) {
      e.isActive = false;
      e.removeEntityToUpdate();
    });
    e.prototype.toDead.call(this);
    this.game.passType == $2MChains.MChains.PassType.Move && 0 == this.game.chainsList.length && this.game.bronMonsterMgr.changeGameStatus($2MChains.MChains.RoundStatus.TRANSITIONAM);
  };
  return cc__decorate([ccp_ccclass], _ctor);
}($2Dragon.Dragon);
exports.MCDragon = exp_MCDragon;