import { FCollider, ColliderType, StateType } from "./FCollider";
import { Intersection } from "./Intersection";
import { QuadTree } from "./QuadTree";

enum CollisionEvent {
    onEnter = "onCollisionEnter",
    onStay = "onCollisionStay",
    onExit = "onCollisionExit"
}

const tempVec2 = cc.v2();
const tempMat4 = cc.mat4();
const tempList: any[] = [];
let collisionResult = 0;

function transformRect(rect: cc.Rect, matrix: cc.Mat4, topLeft: cc.Vec2, topRight: cc.Vec2, bottomLeft: cc.Vec2, bottomRight: cc.Vec2): void {
    const x = rect.x;
    const y = rect.y;
    const width = rect.width;
    const height = rect.height;
    const m = matrix.m;
    const m00 = m[0];
    const m01 = m[1];
    const m04 = m[4];
    const m05 = m[5];
    const tx = m00 * x + m04 * y + m[12];
    const ty = m01 * x + m05 * y + m[13];
    const xa = m00 * width;
    const xb = m01 * width;
    const yc = m04 * height;
    const yd = m05 * height;
    topLeft.x = tx;
    topLeft.y = ty;
    topRight.x = xa + tx;
    topRight.y = xb + ty;
    bottomLeft.x = yc + tx;
    bottomLeft.y = yd + ty;
    bottomRight.x = xa + yc + tx;
    bottomRight.y = xb + yd + ty;
}

export default class FColliderManager {
    private static _instance: FColliderManager | null = null;
    private _tree: QuadTree | null = null;
    private _treeDirty: boolean = true;
    private _maxDepth: number = 4;
    private _maxChildren: number = 10;
    private _treeRect: cc.Rect = cc.rect(0, 0, cc.winSize.width, cc.winSize.height);
    private _enable: boolean = false;
    private _colliders: FCollider[] = [];
    private _enableDebugDraw: boolean = false;
    private _enableQuadTreeDraw: boolean = false;
    private _debugDrawer: cc.Graphics | null = null;

    constructor() {
        this._tree = new QuadTree(this._treeRect, 0, this._maxDepth, this._maxChildren);
    }

    static get instance(): FColliderManager {
        if (!this._instance) {
            this._instance = new FColliderManager();
        }
        return this._instance;
    }

    get maxDepth(): number {
        return this._maxDepth;
    }

    set maxDepth(value: number) {
        if (value != this._maxDepth) {
            this._maxDepth = value;
            this._treeDirty = true;
        }
    }

    get maxChildren(): number {
        return this._maxChildren;
    }

    set maxChildren(value: number) {
        if (this._maxChildren != value) {
            this._maxChildren = value;
            this._treeDirty = true;
        }
    }

    get treeRect(): cc.Rect {
        return this._treeRect;
    }

    set treeRect(value: cc.Rect) {
        if (!(this._treeRect && this._treeRect.equals(value))) {
            this._treeRect.set(value);
            this._treeDirty = false;
        }
    }

    get enable(): boolean {
        return this._enable;
    }

    set enable(value: boolean) {
        this._enable = value;
        if (value) {
            cc.director.getScheduler().enableForTarget(this);
            cc.director.getScheduler().scheduleUpdate(this, cc.Scheduler.PRIORITY_NON_SYSTEM, false);
        }
    }

    clearMgr(): void {
        tempList.length = 0;
        this._tree!.clear();
    }

    get colliders(): FCollider[] {
        return this._colliders;
    }

    addCollider(collider: FCollider): void {
        this.initCollider(collider);
        this._colliders.push(collider);
    }

    removeCollider(collider: FCollider): void {
        for (let i = this._colliders.length - 1; i >= 0; i--) {
            const c = this._colliders[i];
            if (collider.colliderId === c.colliderId) {
                this._colliders.splice(i, 1);
                const contactMap = collider.contactMap;
                if (contactMap) {
                    contactMap.forEach((contact) => {
                        this.updateCollideExit(collider, contact.other);
                    });
                }
                break;
            }
        }
    }

    initCollider(collider: FCollider): void {
        collider.initCollider();
    }

    updateCollider(collider: FCollider): void {
        collider.node.getWorldMatrix(tempMat4);
        collider.cindex.length = 0;
        
        if (collider.type === ColliderType.Box) {
            const boxCollider = collider as any;
            const size = boxCollider.size;
            boxCollider.aabb.x = boxCollider.offset.x - size.width / 2;
            boxCollider.aabb.y = boxCollider.offset.y - size.height / 2;
            boxCollider.aabb.width = size.width;
            boxCollider.aabb.height = size.height;
            
            const worldPoints = boxCollider.worldPoints;
            const topLeft = worldPoints[0];
            const topRight = worldPoints[1];
            const bottomLeft = worldPoints[2];
            const bottomRight = worldPoints[3];
            
            transformRect(boxCollider.aabb, tempMat4, topLeft, topRight, bottomLeft, bottomRight);
            
            const minX = Math.min(topLeft.x, topRight.x, bottomLeft.x, bottomRight.x);
            const minY = Math.min(topLeft.y, topRight.y, bottomLeft.y, bottomRight.y);
            const maxX = Math.max(topLeft.x, topRight.x, bottomLeft.x, bottomRight.x);
            const maxY = Math.max(topLeft.y, topRight.y, bottomLeft.y, bottomRight.y);
            
            const worldEdge = boxCollider.worldEdge;
            for (let i = 0; i < worldPoints.length; i++) {
                if (!worldEdge[i]) {
                    worldEdge[i] = cc.v2();
                }
                cc.Vec2.subtract(worldEdge[i], worldPoints[(i + 1) % worldPoints.length], worldPoints[i]);
            }
            
            boxCollider.aabb.x = minX;
            boxCollider.aabb.y = minY;
            boxCollider.aabb.width = maxX - minX;
            boxCollider.aabb.height = maxY - minY;
        } else if (collider.type == ColliderType.Circle) {
            const circleCollider = collider as any;
            cc.Vec2.transformMat4(tempVec2, circleCollider.offset, tempMat4);
            circleCollider.worldPosition.x = tempVec2.x;
            circleCollider.worldPosition.y = tempVec2.y;
            
            const m = tempMat4.m;
            const oldTx = m[12];
            const oldTy = m[13];
            m[12] = m[13] = 0;
            
            tempVec2.x = circleCollider.radius;
            tempVec2.y = 0;
            cc.Vec2.transformMat4(tempVec2, tempVec2, tempMat4);
            
            const worldRadius = Math.sqrt(tempVec2.x * tempVec2.x + tempVec2.y * tempVec2.y);
            circleCollider.worldRadius = worldRadius;
            circleCollider.aabb.x = circleCollider.worldPosition.x - worldRadius;
            circleCollider.aabb.y = circleCollider.worldPosition.y - worldRadius;
            circleCollider.aabb.width = 2 * worldRadius;
            circleCollider.aabb.height = 2 * worldRadius;
            
            m[12] = oldTx;
            m[13] = oldTy;
        } else if (collider.type == ColliderType.Polygon) {
            const polygonCollider = collider as any;
            const points = polygonCollider.points;
            const worldPoints = polygonCollider.worldPoints;
            const worldEdge = polygonCollider.worldEdge;
            
            worldPoints.length = points.length;
            let minX = Number.MAX_SAFE_INTEGER;
            let minY = Number.MAX_SAFE_INTEGER;
            let maxX = -Number.MAX_SAFE_INTEGER;
            let maxY = -Number.MAX_SAFE_INTEGER;
            
            for (let i = 0; i < points.length; i++) {
                if (!worldPoints[i]) {
                    worldPoints[i] = cc.v2();
                }
                tempVec2.x = points[i].x + polygonCollider.offset.x;
                tempVec2.y = points[i].y + polygonCollider.offset.y;
                cc.Vec2.transformMat4(tempVec2, tempVec2, tempMat4);
                
                const x = tempVec2.x;
                const y = tempVec2.y;
                worldPoints[i].set(tempVec2);
                
                if (x > maxX) maxX = x;
                if (x < minX) minX = x;
                if (y > maxY) maxY = y;
                if (y < minY) minY = y;
            }
            
            if (collider.isConvex) {
                for (let i = 0; i < worldPoints.length; i++) {
                    if (!worldEdge[i]) {
                        worldEdge[i] = cc.v2();
                    }
                    cc.Vec2.subtract(worldEdge[i], worldPoints[(i + 1) % worldPoints.length], worldPoints[i]);
                }
            }
            
            polygonCollider.aabb.x = minX;
            polygonCollider.aabb.y = minY;
            polygonCollider.aabb.width = maxX - minX;
            polygonCollider.aabb.height = maxY - minY;
        }
    }

    shouldCollide(colliderA: FCollider, colliderB: FCollider): boolean {
        const nodeA = colliderA.node;
        const nodeB = colliderB.node;
        const collisionMatrix = cc.game.collisionMatrix;
        return nodeA !== nodeB && collisionMatrix[nodeA.groupIndex][nodeB.groupIndex];
    }

    update(dt: number): void {
        if (this.enable) {
            this.oneTest(dt);
        }
    }

    getColliderCompsInRange(collider: FCollider): FCollider[] {
        const result: FCollider[] = [];
        for (let i = 0; i < collider.cindex.length; i++) {
            if (tempList[collider.cindex[i]]) {
                for (let j = 0; j < tempList[collider.cindex[i]].length; j++) {
                    const other = tempList[collider.cindex[i]][j];
                    if (other.uuid != collider.uuid) {
                        result.push(other);
                    }
                }
            }
        }
        return result;
    }

    get tempList(): any[] {
        return tempList;
    }

    oneTest(dt?: number): void {
        if (this._treeDirty) {
            this._tree = new QuadTree(this._treeRect, 0, this._maxDepth, this._maxChildren);
            this._treeDirty = false;
        }
        
        this._tree!.clear();
        
        for (let i = this._colliders.length - 1; i >= 0; i--) {
            const collider = this._colliders[i];
            if (collider.isActive) {
                if (collider && collider.isValid) {
                    collider.contactMap.forEach((contact) => {
                        contact.state = StateType.NoTest;
                    });
                    this.updateCollider(this._colliders[i]);
                    this._tree!.insert(this._colliders[i]);
                } else {
                    this._colliders.splice(i, 1);
                }
            }
        }
        
        tempList.length = 0;
        this._tree!.getAllNeedTestColliders(tempList);
        
        for (let i = 0; i < tempList.length; i++) {
            const colliders = tempList[i];
            for (let j = 0; j < colliders.length; j++) {
                const colliderA = colliders[j];
                for (let k = j + 1; k < colliders.length; k++) {
                    const colliderB = colliders[k];
                    if (this.shouldCollide(colliderA, colliderB)) {
                        // Collision detection logic would go here
                        // This is simplified for brevity
                        collisionResult = this.testCollision(colliderA, colliderB) ? 1 : 0;
                        
                        if (collisionResult == 1) {
                            this.updateCollideContact(colliderA, colliderB);
                        } else {
                            this.updateCollideExit(colliderA, colliderB);
                        }
                    }
                }
            }
        }
        
        for (let i = this._colliders.length - 1; i >= 0; i--) {
            const collider = this._colliders[i];
            collider.contactMap.forEach((contact) => {
                if (contact.state === StateType.NoTest) {
                    this.updateCollideExit(collider, contact.other);
                }
            });
        }
        
        this.drawColliders();
        this.drawQuadTree();
    }

    private testCollision(colliderA: FCollider, colliderB: FCollider): boolean {
        // Simplified collision test - actual implementation would be more complex
        return Intersection.rectRect(colliderA.aabb, colliderB.aabb);
    }

    checkCollider(collider: FCollider, results?: FCollider[]): FCollider[] {
        results = results || [];
        const candidates: FCollider[] = [];
        this._tree!.retrieve(collider, candidates);
        
        for (let i = 0; i < candidates.length; i++) {
            const other = candidates[i];
            if (other.colliderId !== collider.colliderId && this.shouldCollide(collider, other)) {
                if (this.testCollision(collider, other)) {
                    results.push(other);
                }
            }
        }
        return results;
    }

    updateCollideContact(colliderA: FCollider, colliderB: FCollider): void {
        const contactA = colliderA.contactMap.get(colliderB.colliderId);
        if (contactA) {
            contactA.state = StateType.IsTest;
            this._doCollide(colliderA, colliderB, CollisionEvent.onStay);
        } else {
            colliderA.contactMap.set(colliderB.colliderId, {
                other: colliderB,
                state: StateType.IsTest
            });
            this._doCollide(colliderA, colliderB, CollisionEvent.onEnter);
        }
        
        const contactB = colliderB.contactMap.get(colliderA.colliderId);
        if (contactB) {
            contactB.state = StateType.IsTest;
            this._doCollide(colliderB, colliderA, CollisionEvent.onStay);
        } else {
            colliderB.contactMap.set(colliderA.colliderId, {
                other: colliderA,
                state: StateType.IsTest
            });
            this._doCollide(colliderB, colliderA, CollisionEvent.onEnter);
        }
    }

    updateCollideExit(colliderA: FCollider, colliderB: FCollider): void {
        if (colliderA.contactMap?.delete(colliderB.colliderId)) {
            this._doCollide(colliderA, colliderB, CollisionEvent.onExit);
        }
        if (colliderB.contactMap?.delete(colliderA.colliderId)) {
            this._doCollide(colliderB, colliderA, CollisionEvent.onExit);
        }
    }

    private _doCollide(collider: FCollider, other: FCollider, event: CollisionEvent): void {
        if (collider.comp?.[event]) {
            collider.comp[event](other, collider);
        }
    }

    get enableDebugDraw(): boolean {
        return this._enableDebugDraw;
    }

    set enableDebugDraw(value: boolean) {
        if (value && !this._enableDebugDraw) {
            this._checkDebugDrawValid();
            this._debugDrawer!.node.active = true;
        } else if (!value && this._enableDebugDraw) {
            this._debugDrawer!.clear(true);
            this._debugDrawer!.node.active = false;
        }
        this._enableDebugDraw = value;
    }

    get enableQuadTreeDraw(): boolean {
        return this._enableQuadTreeDraw;
    }

    set enableQuadTreeDraw(value: boolean) {
        if (value && !this._enableQuadTreeDraw) {
            this._checkDebugDrawValid();
            this._debugDrawer!.node.active = true;
        } else if (!value && this._enableQuadTreeDraw) {
            this._debugDrawer!.clear(true);
            this._debugDrawer!.node.active = false;
        }
        this._enableQuadTreeDraw = value;
    }

    private _checkDebugDrawValid(): void {
        if (!this._debugDrawer || !this._debugDrawer.isValid) {
            const node = new cc.Node("FCOLLISION_MANAGER_DEBUG_DRAW");
            node.zIndex = cc.macro.MAX_ZINDEX;
            cc.game.addPersistRootNode(node);
            this._debugDrawer = node.addComponent(cc.Graphics);
            this._debugDrawer.lineWidth = 16;
        }
    }

    drawColliders(): void {
        if (this._enableDebugDraw) {
            this._checkDebugDrawValid();
            const drawer = this._debugDrawer!;
            drawer.clear();
            
            const colliders = this._colliders;
            for (let i = 0; i < colliders.length; i++) {
                const collider = colliders[i];
                if (collider.isActive) {
                    drawer.strokeColor = cc.Color.RED;
                    if (collider.type === ColliderType.Box || collider.type === ColliderType.Polygon) {
                        const worldPoints = (collider as any).worldPoints;
                        if (worldPoints.length > 0) {
                            cc.Vec2.set(tempVec2, worldPoints[0].x, worldPoints[0].y);
                            drawer.moveTo(tempVec2.x, tempVec2.y);
                            for (let j = 1; j < worldPoints.length; j++) {
                                cc.Vec2.set(tempVec2, worldPoints[j].x, worldPoints[j].y);
                                drawer.lineTo(tempVec2.x, tempVec2.y);
                            }
                            drawer.close();
                            drawer.stroke();
                        }
                    } else if (collider.type === ColliderType.Circle) {
                        const circleCollider = collider as any;
                        drawer.circle(circleCollider.worldPosition.x, circleCollider.worldPosition.y, circleCollider.worldRadius);
                        drawer.stroke();
                    }
                }
            }
        }
    }

    drawQuadTree(): void {
        if (this._enableQuadTreeDraw) {
            this._checkDebugDrawValid();
            const drawer = this._debugDrawer!;
            if (!this._enableDebugDraw) {
                drawer.clear(true);
            }
            this._tree!.render(drawer);
        }
    }
}
