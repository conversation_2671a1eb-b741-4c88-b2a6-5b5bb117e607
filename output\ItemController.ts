// ItemController.ts
// 从 ItemController.js 转换而来

import { CallID } from "./CallID";
import { Cfg } from "./Cfg";
import { CurrencyConfigCfg } from "./CurrencyConfigCfg";
import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { UIManager } from "./UIManager";
import { AlertManager } from "./AlertManager";
import { ModeBackpackHeroModel } from "./ModeBackpackHeroModel";
import { ItemModel } from "./ItemModel";

/**
 * 道具控制器
 */
export class ItemController extends MVC.Controller {
    private itemModel: ItemModel;

    constructor() {
        super();
        this.itemModel = Manager.getModel(ItemModel);
    }

    /**
     * 初始化监听
     */
    initListeners(): void {
        this.addListener(ListenID.USE_ITEM, this.onUseItem, this);
        this.addListener(ListenID.GET_ITEM, this.onGetItem, this);
        this.addListener(ListenID.ITEM_COUNT_CHANGE, this.onItemCountChange, this);
    }

    /**
     * 使用道具
     * @param itemId 道具ID
     * @param count 使用数量
     */
    onUseItem(itemId: number, count: number = 1): void {
        if (this.itemModel.hasItem(itemId, count)) {
            this.itemModel.useItem(itemId, count);
            this.applyItemEffect(itemId, count);
            Notifier.send(ListenID.ITEM_USED, { itemId, count });
        } else {
            AlertManager.showAlert("道具数量不足");
        }
    }

    /**
     * 获得道具
     * @param itemId 道具ID
     * @param count 获得数量
     */
    onGetItem(itemId: number, count: number): void {
        this.itemModel.addItem(itemId, count);
        Notifier.send(ListenID.ITEM_OBTAINED, { itemId, count });
    }

    /**
     * 道具数量变化
     * @param data 变化数据
     */
    onItemCountChange(data: any): void {
        this.updateItemUI(data.itemId);
    }

    /**
     * 应用道具效果
     * @param itemId 道具ID
     * @param count 使用数量
     */
    private applyItemEffect(itemId: number, count: number): void {
        const itemConfig = Cfg.ItemCfg.get(itemId);
        if (!itemConfig) return;

        switch (itemConfig.type) {
            case 1: // 货币类
                this.applyCurrencyEffect(itemConfig, count);
                break;
            case 2: // 经验类
                this.applyExpEffect(itemConfig, count);
                break;
            case 3: // 装备类
                this.applyEquipEffect(itemConfig, count);
                break;
            default:
                console.log("未知道具类型:", itemConfig.type);
        }
    }

    /**
     * 应用货币效果
     * @param itemConfig 道具配置
     * @param count 数量
     */
    private applyCurrencyEffect(itemConfig: any, count: number): void {
        const currencyType = itemConfig.effectParam1;
        const amount = itemConfig.effectParam2 * count;

        const currencyConfig = CurrencyConfigCfg.get(currencyType);
        if (currencyConfig) {
            Manager.getModel(ItemModel).addCurrency(currencyType, amount);
        }
    }

    /**
     * 应用经验效果
     * @param itemConfig 道具配置
     * @param count 数量
     */
    private applyExpEffect(itemConfig: any, count: number): void {
        const expAmount = itemConfig.effectParam1 * count;
        const heroModel = Manager.getModel(ModeBackpackHeroModel);
        heroModel.addExp(expAmount);
    }

    /**
     * 应用装备效果
     * @param itemConfig 道具配置
     * @param count 数量
     */
    private applyEquipEffect(itemConfig: any, count: number): void {
        const equipId = itemConfig.effectParam1;
        for (let i = 0; i < count; i++) {
            this.itemModel.addEquip(equipId);
        }
    }

    /**
     * 更新道具UI
     * @param itemId 道具ID
     */
    private updateItemUI(itemId: number): void {
        UIManager.instance.updateItemDisplay(itemId);
    }
}

export { ItemController };