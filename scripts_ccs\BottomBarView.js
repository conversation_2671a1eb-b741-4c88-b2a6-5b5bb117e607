var i;
var cc__extends = __extends;
var cc__decorate = __decorate;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.BottomBarView = undefined;
var $2CallID = require("CallID");
var $2SoundCfg = require("SoundCfg");
var $2MVC = require("MVC");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2Game = require("Game");
var $2BottomBarModel = require("BottomBarModel");
var cc__decorator = cc._decorator;
var ccp_ccclass = cc__decorator.ccclass;
var ccp_property = cc__decorator.property;
var ccp_menu = cc__decorator.menu;
var m = ["人物", "伙伴", "主界面", "抽卡", "副本"];
var _ = ["", "", "", "ui/ModeBackpackHero/M20_PrePare_MenuView", ""];
var exp_BottomBarView = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.panelListPrefab = [];
    t.pageView = null;
    t.gameView = null;
    t.btnBottom = [];
    t.btnRoot = null;
    t._viewBgm = $2SoundCfg.SoundDefine.bgm_lobby;
    t._turnPageTime = .2;
    t._curSelectIndex = 0;
    t._subViewList = [];
    return t;
  }
  cc__extends(_ctor, e);
  Object.defineProperty(_ctor.prototype, "model", {
    get: function () {
      return $2BottomBarModel.default.instance;
    },
    enumerable: false,
    configurable: true
  });
  _ctor.prototype.setInfo = function () {
    cc.log("[setInfo]============================");
    if ($2Notifier.Notifier.call($2CallID.CallID.Fight_GetCutMode) == $2Game.Game.Mode.NONE && $2Manager.Manager.vo.switchVo.m20guideToggle[0] && 1 == $2Manager.Manager.vo.userVo.guideIndex && 1 == $2Manager.Manager.vo.switchVo.isEnterBackpack && !cc.sys.language.includes("vn")) {
      var e = $2Game.Game.Mode.CHAINS;
      var t = $2Game.Game.getMouth(e);
      $2Notifier.Notifier.send(t.mouth, e, $2MVC.MVC.openArgs().setParam({
        id: 1
      }));
    } else {
      this.scrollToPage(3);
    }
  };
  _ctor.prototype.changeListener = function () {};
  _ctor.prototype.scrollToPage = function (e, t) {
    var o = this;
    undefined === t && (t = this._turnPageTime);
    var i = Math.floor(m.length / 2);
    if (this._subViewList[e]) {
      this._subViewList[e].setOpenArgs(this._openArgs);
      3 == e && this._subViewList[e].open();
    } else {
      var n = m.length;
      var r = -Math.floor(n / 2);
      var a = function (e, t) {
        e.setPosition((r + t) * $2Manager.Manager.vo.designSize.width, 0);
        var i = e.getComponent($2MVC.MVC.BaseView);
        i.init($2MVC.MVC.eUILayer.Scene, $2MVC.MVC.eUIQueue.None, new $2MVC.MVC.DefaultTransition(), "");
        i.setNodeInfo(o.pageView);
        i.setOpenArgs($2MVC.MVC.openArgs().setIsNeedLoading(false));
        i.open();
        o._subViewList[t] = i;
      };
      if (this.panelListPrefab[e]) {
        var s = cc.instantiate(this.panelListPrefab[e]);
        a(s, e);
      } else {
        $2Manager.Manager.loader.loadPrefab(_[e]).then(function (t) {
          if (o._subViewList[e]) {
            t.destroy();
          } else {
            var i = cc.instantiate(t);
            a(i, e);
          }
        });
      }
    }
    if (this._subViewList[e]) {
      this._subViewList[e].node.opacity = 255;
      2 == e && (this._subViewList[e].node.opacity = 255);
      this._subViewList[e].setUIMaskActive(false);
    }
    this._curSelectIndex = e;
    cc.tween(this.pageView).to(t, {
      x: (i - e) * $2Manager.Manager.vo.designSize.width
    }).call(function () {
      var e = 0;
      for (var t = o._subViewList.length; e < t; e++) {
        var i = o._subViewList[e];
        if (i) {
          if (2 == e) {
            i.node.active = 2 == o._curSelectIndex;
            continue;
          }
          i.node.active = o._curSelectIndex == e;
          i.setUIMaskActive(o._curSelectIndex != e);
        }
      }
    }).start();
    this.refreshMenu();
  };
  _ctor.prototype.onOpen = function () {};
  _ctor.prototype.refreshMenu = function () {};
  _ctor.prototype.onClose = function () {};
  _ctor.prototype.onShowFinish = function () {};
  _ctor.prototype.onHideFinish = function () {};
  _ctor.prototype.onShow = function () {};
  _ctor.prototype.onHide = function () {};
  cc__decorate([ccp_property([cc.Prefab])], _ctor.prototype, "panelListPrefab", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "pageView", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "gameView", undefined);
  cc__decorate([ccp_property(cc.Label)], _ctor.prototype, "btnBottom", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "btnRoot", undefined);
  return cc__decorate([ccp_ccclass, ccp_menu("ViewComponent/BottomBar/BottomBarView"), $2MVC.MVC.uilayer($2MVC.MVC.eUILayer.Main), $2MVC.MVC.uiqueue($2MVC.MVC.eUIQueue.Scene)], _ctor);
}($2MVC.MVC.BaseView);
exports.BottomBarView = exp_BottomBarView;