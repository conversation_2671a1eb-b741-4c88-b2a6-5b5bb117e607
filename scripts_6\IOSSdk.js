var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $2CallID = require("CallID");
var $2GameSeting = require("GameSeting");
var $2ListenID = require("ListenID");
var $2Notifier = require("Notifier");
var $2Manager = require("Manager");
var $2BaseSdk = require("BaseSdk");
cc.nativeAndroid = cc.nativeAndroid || {};
cc.nativeAndroid;
var p = window.jsb && jsb.reflection ? jsb.reflection.callStaticMethod : function () {};
var def_IOSSdk = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t.defaultClass = "org/cocos2dx/javascript/AppActivity";
    t.onbannerShow = function () {};
    t._shareCall = null;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.logout = function () {};
  _ctor.prototype.init = function (t) {
    var o = this;
    e.prototype.init.call(this, t);
    $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Event_SendEvent, this.sendEvent, this);
    $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Shop_RecoveryBuy, this.toBuyRestore, this);
    $2Notifier.Notifier.changeListener(true, $2ListenID.ListenID.Platform_ShowTestAD, this.showTestAD, this);
    $2Notifier.Notifier.changeCall(true, $2CallID.CallID.Shop_GetProductList, this.getProductList, this);
    window.iOSSendMsg = function (e) {
      if ("playAds" == e) {
        $2Manager.Manager.audio.pauseMusic();
        cc.director.pause();
        cc.game.pause();
      }
      if (!("playAdsEnd" != e && "unityAds1" != e && "rewardPlayEnd" != e && "rewardPlayEnd" != e)) {
        cc.director.resume();
        cc.game.resume();
        $2Manager.Manager.audio.resumeMusic();
        "unityAds1" != e && "rewardPlayEnd" != e || o.onPlayEnd && o.onPlayEnd($2BaseSdk.VideoAdCode.COMPLETE, "看完广告");
        "unityAds0" != e && "rewardPlayNotEnd" != e || o.onPlayEnd && o.onPlayEnd($2BaseSdk.VideoAdCode.NOT_COMPLITE, "未完整观看广告");
      }
      return "abcd";
    };
    window.iOSBuySendMsg = function (e) {
      console.log("[iOSBuySendMsg]", e);
      var t = e.split("-");
      switch (t[0]) {
        case "gaiIOSProductList":
          var o = [];
          t[1].split(",").forEach(function (e) {
            if (e) {
              var t = e.split(":");
              o.push({
                product: t[0],
                formattedPrice: t[1],
                formPrice: t[1]
              });
            }
          });
          console.log("[list]:" + JSON.stringify(o));
          $2Notifier.Notifier.send($2ListenID.ListenID.Shop_ShopItemList, o);
          break;
        case "0":
          cc.game.emit("payFinish", 0, "购买失败");
          break;
        default:
          var i = e;
          cc.game.emit("payFinish", 200, i);
      }
      return "abcd";
    };
  };
  _ctor.prototype.login = function (e) {
    return new Promise(function (t) {
      e && e(null);
      t(null);
    });
  };
  _ctor.prototype.showBannerWithNode = function (e, t, o) {
    this.showBannerWithStyle(e, {}, o);
  };
  _ctor.prototype.showBannerWithStyle = function (e, t, o) {
    this.onbannerShow = o;
    p("AppController", "showBannerAds1");
  };
  _ctor.prototype.hideBanner = function () {
    p("AppController", "hiddenBanner");
  };
  _ctor.prototype.destroyBanner = function () {};
  _ctor.prototype.showVideoAD = function (e, t) {
    this.onPlayEnd = t;
    p("AppController", "showAds");
  };
  _ctor.prototype.showFullVideoAD = function (e, t) {
    this.onFullPlayEnd = t;
    p("AppController", "fullscreenAds");
  };
  _ctor.prototype.showTestAD = function () {
    console.log("[showTestAD]");
    p("AppController", "showTestAD");
  };
  _ctor.prototype.sendEvent = function (e, t) {
    null != t && "" != t && "none" != t || (t = "{}");
    "reward_btn" == e && cc.sys.getNetworkType() == cc.sys.NetworkType.NONE || console.log("cc", "sendMsg", JSON.stringify(t));
  };
  _ctor.prototype.vibrate = function (e) {
    undefined === e && (e = 0);
    window.jsb && window.jsb.reflection && jsb.reflection.callStaticMethod("AppController", 0 == e ? "ddd2" : "ddd3");
  };
  _ctor.prototype.share = function () {};
  _ctor.prototype.showInsertAd = function () {};
  _ctor.prototype.showSplashAd = function () {};
  _ctor.prototype.showPrivacy = function (e) {
    this._privacyCallback = e;
    e && e(true);
  };
  _ctor.prototype.goRate = function () {};
  _ctor.prototype.showDebugAdView = function () {};
  _ctor.prototype.toPay = function (e) {
    p("AppController", "iOSBuy:", e.goodsId + "");
  };
  _ctor.prototype.toSubscribe = function (e) {
    p("AppController", "iOSBuy:", e.goodsId + "");
  };
  _ctor.prototype.toBuyRestore = function () {
    console.log("[toBuyRestore]");
    $2Manager.Manager.vo.knapsackVo.addGoods("BuyRestoreBtn", $2GameSeting.GameSeting.GoodsType.System);
    p("AppController", "toBuyRestore");
  };
  _ctor.prototype.getProductList = function () {
    var e = jsb.reflection.callStaticMethod("AppController", "gaiIOSProductList");
    console.log("[getProductList]" + typeof e);
    console.log(e);
    var t = "string" == typeof e ? JSON.parse(e) : e;
    $2Notifier.Notifier.send($2ListenID.ListenID.Shop_ShopItemList, t);
    return t;
  };
  _ctor.prototype.payCallback = function (e) {
    console.log("yjr支付回调返回", e);
    if ("payFail" == e) {
      cc.game.emit("buy-removeAdFail");
    } else {
      "paySuccessful" == e && cc.game.emit("buy-removeAdSuc");
    }
  };
  _ctor.prototype.toShareFaceBook = function (e) {
    this._shareCall = e;
    p(this.defaultClass, "toShareFaceBook", "()V");
  };
  return _ctor;
}($2BaseSdk.BaseSdk);
exports.default = def_IOSSdk;