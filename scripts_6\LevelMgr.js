var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Level = undefined;
var $2Cfg = require("Cfg");
var $2Manager = require("Manager");
var $2ItemModel = require("ItemModel");
var $2ModeBackpackHeroModel = require("ModeBackpackHeroModel");
var $2RecordVo = require("RecordVo");
(function (e) {
  var t = function () {
    this.curday = new Date(Date.now()).getDate();
    this.sweep_count = $2Manager.Manager.vo.switchVo.lvSweep[0] + ($2Manager.Manager.Shop.Subscrib_30 ? 1 : 0) + ($2Manager.Manager.Shop.Subscrib_Long ? 3 : 0);
    this.sweep_count_ad = $2Manager.Manager.vo.switchVo.lvSweep[1];
    this.isADSweepNum = false;
  };
  e.DailyData = t;
  var o = function (e) {
    function o() {
      var o = null !== e && e.apply(this, arguments) || this;
      o.dailyData = new t();
      o.lvRoundReward = {};
      o.lvRound = {};
      o.lvIdUnlock = [1];
      o.curPassLv = 0;
      o.playNum = 0;
      return o;
    }
    cc__extends(o, e);
    return o;
  }($2RecordVo.RecordVo.Data);
  e.Data = o;
  var i = function (e) {
    function t() {
      return null !== e && e.apply(this, arguments) || this;
    }
    cc__extends(t, e);
    t.prototype.getRewardlist = function (e) {
      var t = [];
      $2Cfg.Cfg.BagModeLv.find({
        lvid: e
      }).gameWinReward.forEach(function (e) {
        var o = e[0];
        var i = e[1];
        e[2];
        i = Math.floor(i);
        var n = $2ItemModel.default.instance.briefRewardArrTo([[o, i]]);
        n.forEach(function (t) {
          return t.param = e[2];
        });
        t.push.apply(t, n);
      });
      return t;
    };
    t.prototype.getLvMaxRound = function (e) {
      return this.vo.lvRound[e] || 0;
    };
    t.prototype.setLvMaxRound = function (e, t, o) {
      var i = $2Cfg.Cfg.BagModeLv.get(e);
      var n = 1 == i.type;
      if (o && n) {
        this.vo.curPassLv = Math.max(this.vo.curPassLv, e);
        this.setUnlockPassRound(e);
      }
      o && i.unlockLvid && this.vo.lvIdUnlock.add(i.unlockLvid[0]);
      t > this.getLvMaxRound(e) && (this.vo.lvRound[e] = t);
    };
    t.prototype.setUnlockPassRound = function (e) {
      $2Cfg.Cfg.BagModeLv.get(e);
      var t = $2Cfg.Cfg.BagModeLv.get(e + 1);
      if (t && t.unlockLvid && t.lvid > this.vo.curPassLv) {
        this.vo.lvIdUnlock.add(t.lvid);
        $2ModeBackpackHeroModel.default.instance.checkUnlockEquip();
      }
    };
    Object.defineProperty(t.prototype, "CurChallengeLv", {
      get: function () {
        var e = $2Cfg.Cfg.BagModeLv.getArray().filter(function (e) {
          return 1 == e.type;
        });
        return Math.min(e.lastVal.lvid, this.vo.curPassLv + 1);
      },
      enumerable: false,
      configurable: true
    });
    t.prototype.getPassMainID = function (e) {
      var t = $2Cfg.Cfg.BagModeLv.getArray().find(function (t) {
        var o;
        if (null === (o = t.unlockLvid) || undefined === o) {
          return undefined;
        } else {
          return o.includes(e);
        }
      });
      if (t) {
        return t.lvid;
      } else {
        return e;
      }
    };
    t.prototype.checkLvIsPass = function (e) {
      var t = $2Cfg.Cfg.BagModeLv.get(e);
      return this.getLvMaxRound(e) >= t.wave.lastVal;
    };
    t.prototype.getLvRewardData = function (e, t) {
      var o;
      if (null === (o = this.vo.lvRoundReward[e]) || undefined === o) {
        return undefined;
      } else {
        return o.includes(t);
      }
    };
    t.prototype.setLvRewardData = function (e, t) {
      var o;
      (o = this.vo.lvRoundReward)[e] || (o[e] = []);
      this.vo.lvRoundReward[e].add(t);
    };
    return t;
  }($2RecordVo.RecordVo.Mgr);
  e.Mgr = i;
})(exports.Level || (exports.Level = {}));