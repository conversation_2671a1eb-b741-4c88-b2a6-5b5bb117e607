{"timestamp": "2025-07-09T03:21:59.147Z", "totalAnalyzed": 172, "ccclassFiles": 16, "nonCcclassFiles": 156, "errorFiles": 0, "successfullyMoved": 16, "failedToMove": 0, "singleCcclassFiles": 0, "multiCcclassFiles": 16, "remainingInScripts5": 156, "totalInScriptsCcs": 130, "movedFiles": [{"fileName": "BottomBarView.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "BuffList.js", "ccclassCount": 35, "totalDefinitions": 36, "isSingleClass": false, "isMultiClass": true}, {"fileName": "Dragon.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "FightScene.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "FightUIView.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "LoadingView.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "M33_FightScene.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "M33_FightUIView.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "MCBoss.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "MCDragoMutilation.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "MCDragon.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "Pop.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "RoleSkillList.js", "ccclassCount": 35, "totalDefinitions": 36, "isSingleClass": false, "isMultiClass": true}, {"fileName": "SettingView.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "SkillModule.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}, {"fileName": "TestView.js", "ccclassCount": 1, "totalDefinitions": 2, "isSingleClass": false, "isMultiClass": true}]}