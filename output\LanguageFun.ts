// LanguageFun.ts
// 从 LanguageFun.js 转换而来

import { SdkConfig } from "./SdkConfig";

/**
 * 语言功能类
 * 处理多语言相关功能
 */
export class LanguageFun {
    
    /**
     * 获取当前语言
     * @returns 当前语言代码
     */
    static getCurrentLanguage(): string {
        return SdkConfig.language || "zh";
    }
    
    /**
     * 设置语言
     * @param language 语言代码
     */
    static setLanguage(language: string): void {
        SdkConfig.language = language;
    }
    
    /**
     * 获取本地化文本
     * @param key 文本键
     * @param defaultText 默认文本
     * @returns 本地化文本
     */
    static getText(key: string, defaultText?: string): string {
        const currentLang = this.getCurrentLanguage();
        // 这里应该从语言配置文件中获取对应的文本
        // 暂时返回默认文本或键值
        return defaultText || key;
    }
    
    /**
     * 检查是否为中文
     * @returns 是否为中文
     */
    static isChinese(): boolean {
        const lang = this.getCurrentLanguage();
        return lang === "zh" || lang === "zh-CN" || lang === "zh-TW";
    }
    
    /**
     * 检查是否为英文
     * @returns 是否为英文
     */
    static isEnglish(): boolean {
        const lang = this.getCurrentLanguage();
        return lang === "en" || lang === "en-US";
    }
    
    /**
     * 格式化文本（支持参数替换）
     * @param template 模板文本
     * @param params 参数对象
     * @returns 格式化后的文本
     */
    static formatText(template: string, params: { [key: string]: any }): string {
        let result = template;
        for (const key in params) {
            const placeholder = `{${key}}`;
            result = result.replace(new RegExp(placeholder, 'g'), params[key]);
        }
        return result;
    }
    
    /**
     * 获取支持的语言列表
     * @returns 支持的语言列表
     */
    static getSupportedLanguages(): string[] {
        return ["zh", "en", "ja", "ko"];
    }
    
    /**
     * 检查语言是否支持
     * @param language 语言代码
     * @returns 是否支持
     */
    static isLanguageSupported(language: string): boolean {
        return this.getSupportedLanguages().includes(language);
    }
}

export { LanguageFun };
