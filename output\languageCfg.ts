// languageCfg.ts
// 从 languageCfg.js 转换而来

import { TConfig } from "./TConfig";

/**
 * 语言定义枚举
 */
export enum languageDefine {
    // 这里应该包含所有语言键值对
    // 示例：
    // HELLO = "hello",
    // WELCOME = "welcome",
    // 实际的枚举值需要根据具体的语言配置文件来定义
}

/**
 * 语言配置读取器
 */
export class languageCfgReader extends TConfig {
    
    constructor() {
        super();
    }
    
    /**
     * 获取语言文本
     * @param key 语言键
     * @param language 语言代码，默认为当前语言
     * @returns 对应的文本
     */
    getText(key: string | number, language?: string): string {
        const config = this.getConfig(key);
        if (!config) {
            console.warn(`语言配置不存在: ${key}`);
            return key.toString();
        }
        
        const currentLanguage = language || this.getCurrentLanguage();
        return config[currentLanguage] || config["zh"] || key.toString();
    }
    
    /**
     * 获取当前语言
     */
    getCurrentLanguage(): string {
        // 从系统设置或用户偏好中获取当前语言
        return cc.sys.language || "zh";
    }
    
    /**
     * 检查语言键是否存在
     * @param key 语言键
     * @returns 是否存在
     */
    hasKey(key: string | number): boolean {
        return this.getConfig(key) !== null;
    }
    
    /**
     * 获取支持的语言列表
     * @returns 支持的语言代码数组
     */
    getSupportedLanguages(): string[] {
        const configs = this.getAllConfigs();
        const languages = new Set<string>();
        
        for (const config of Object.values(configs)) {
            if (typeof config === "object") {
                Object.keys(config).forEach(lang => languages.add(lang));
            }
        }
        
        return Array.from(languages);
    }
    
    /**
     * 格式化文本（支持参数替换）
     * @param key 语言键
     * @param params 参数对象
     * @param language 语言代码
     * @returns 格式化后的文本
     */
    formatText(key: string | number, params: { [key: string]: any }, language?: string): string {
        let text = this.getText(key, language);
        
        // 替换参数
        for (const paramKey in params) {
            const placeholder = `{${paramKey}}`;
            text = text.replace(new RegExp(placeholder, 'g'), params[paramKey]);
        }
        
        return text;
    }
    
    /**
     * 批量获取语言文本
     * @param keys 语言键数组
     * @param language 语言代码
     * @returns 文本对象
     */
    getTexts(keys: (string | number)[], language?: string): { [key: string]: string } {
        const result: { [key: string]: string } = {};
        
        keys.forEach(key => {
            result[key.toString()] = this.getText(key, language);
        });
        
        return result;
    }
    
    /**
     * 获取语言配置的统计信息
     * @returns 统计信息
     */
    getLanguageStats(): {
        totalKeys: number;
        supportedLanguages: string[];
        missingTranslations: { [language: string]: string[] };
    } {
        const configs = this.getAllConfigs();
        const supportedLanguages = this.getSupportedLanguages();
        const missingTranslations: { [language: string]: string[] } = {};
        
        // 初始化缺失翻译记录
        supportedLanguages.forEach(lang => {
            missingTranslations[lang] = [];
        });
        
        // 检查缺失的翻译
        for (const [key, config] of Object.entries(configs)) {
            if (typeof config === "object") {
                supportedLanguages.forEach(lang => {
                    if (!config[lang]) {
                        missingTranslations[lang].push(key);
                    }
                });
            }
        }
        
        return {
            totalKeys: Object.keys(configs).length,
            supportedLanguages,
            missingTranslations
        };
    }
    
    /**
     * 验证语言配置完整性
     * @returns 验证结果
     */
    validateLanguageConfig(): {
        isValid: boolean;
        errors: string[];
        warnings: string[];
    } {
        const errors: string[] = [];
        const warnings: string[] = [];
        const configs = this.getAllConfigs();
        const supportedLanguages = this.getSupportedLanguages();
        
        // 检查每个配置项
        for (const [key, config] of Object.entries(configs)) {
            if (typeof config !== "object") {
                errors.push(`配置项 ${key} 不是对象类型`);
                continue;
            }
            
            // 检查是否有基础语言（中文）
            if (!config["zh"]) {
                warnings.push(`配置项 ${key} 缺少中文翻译`);
            }
            
            // 检查是否有英文翻译
            if (!config["en"]) {
                warnings.push(`配置项 ${key} 缺少英文翻译`);
            }
            
            // 检查空值
            for (const lang of supportedLanguages) {
                if (config[lang] === "" || config[lang] === null || config[lang] === undefined) {
                    warnings.push(`配置项 ${key} 的 ${lang} 翻译为空`);
                }
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }
    
    /**
     * 导出语言配置为JSON
     * @param language 指定语言，不指定则导出所有语言
     * @returns JSON字符串
     */
    exportToJSON(language?: string): string {
        const configs = this.getAllConfigs();
        
        if (language) {
            // 导出指定语言
            const result: { [key: string]: string } = {};
            for (const [key, config] of Object.entries(configs)) {
                if (typeof config === "object" && config[language]) {
                    result[key] = config[language];
                }
            }
            return JSON.stringify(result, null, 2);
        } else {
            // 导出所有语言
            return JSON.stringify(configs, null, 2);
        }
    }
    
    /**
     * 搜索包含指定文本的语言键
     * @param searchText 搜索文本
     * @param language 搜索的语言
     * @returns 匹配的键数组
     */
    searchKeys(searchText: string, language: string = "zh"): string[] {
        const configs = this.getAllConfigs();
        const matchedKeys: string[] = [];
        
        for (const [key, config] of Object.entries(configs)) {
            if (typeof config === "object" && config[language]) {
                if (config[language].includes(searchText)) {
                    matchedKeys.push(key);
                }
            }
        }
        
        return matchedKeys;
    }
}

export { languageCfgReader };
