var i;
var cc__extends = __extends;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $1$2GTAssembler2D = require("GTAssembler2D");
var def_GTSimpleSpriteAssembler2D = function (e) {
  function _ctor() {
    return null !== e && e.apply(this, arguments) || this;
  }
  cc__extends(_ctor, e);
  _ctor.prototype.updateRenderData = function (t) {
    this.packToDynamicAtlas(t, t.spriteFrame);
    e.prototype.updateRenderData.call(this, t);
  };
  _ctor.prototype.updateUVs = function (e) {
    var t = e._spriteFrame.uv;
    var o = this.uvOffset;
    var i = this.floatsPerVert;
    var n = this._renderData.vDatas[0];
    for (var r = 0; r < 4; r++) {
      var a = 2 * r;
      var s = i * r + o;
      n[s] = t[a];
      n[s + 1] = t[a + 1];
    }
  };
  _ctor.prototype.updateVerts = function (e) {
    var t;
    var o;
    var i;
    var n;
    var r = e.node;
    var a = r.width;
    var s = r.height;
    var c = r.anchorX * a;
    var l = r.anchorY * s;
    if (e.trim) {
      t = -c;
      o = -l;
      i = a - c;
      n = s - l;
    } else {
      var u = e.spriteFrame;
      var p = u._originalSize.width;
      var f = u._originalSize.height;
      var h = u._rect.width;
      var d = u._rect.height;
      var g = u._offset;
      var y = a / p;
      var m = s / f;
      var _ = g.x + (p - h) / 2;
      var v = g.x - (p - h) / 2;
      t = _ * y - c;
      o = (g.y + (f - d) / 2) * m - l;
      i = a + v * y - c;
      n = s + (g.y - (f - d) / 2) * m - l;
    }
    var M = this._local;
    M[0] = t;
    M[1] = o;
    M[2] = i;
    M[3] = n;
    this.updateWorldVerts(e);
  };
  return _ctor;
}($1$2GTAssembler2D.default);
exports.default = def_GTSimpleSpriteAssembler2D;