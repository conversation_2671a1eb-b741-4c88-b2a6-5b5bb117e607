// IOSSdk.ts
// 从 IOSSdk.js 转换而来

import { CallID } from "./CallID";
import { GameSeting } from "./GameSeting";
import { ListenID } from "./ListenID";
import { Notifier } from "./Notifier";
import { Manager } from "./Manager";
import { BaseSdk } from "./BaseSdk";

/**
 * iOS SDK类
 * 处理iOS平台相关的SDK功能
 */
export default class IOSSdk extends BaseSdk {

    constructor() {
        super();
        this.initIOSCallbacks();
    }

    /**
     * 初始化iOS回调函数
     */
    private initIOSCallbacks(): void {
        // 设置iOS消息回调
        (window as any).iOSSendMsg = this.onIOSMessage.bind(this);
        (window as any).iOSBuySendMsg = this.onIOSBuyMessage.bind(this);
    }

    /**
     * 处理iOS消息
     * @param message 消息内容
     */
    private onIOSMessage(message: string): void {
        try {
            const data = JSON.parse(message);
            this.handleIOSMessage(data);
        } catch (error) {
            console.error("解析iOS消息失败:", error);
        }
    }

    /**
     * 处理iOS购买消息
     * @param message 购买消息
     */
    private onIOSBuyMessage(message: string): void {
        try {
            const data = JSON.parse(message);
            this.handleIOSBuyMessage(data);
        } catch (error) {
            console.error("解析iOS购买消息失败:", error);
        }
    }

    /**
     * 处理iOS消息
     * @param data 消息数据
     */
    private handleIOSMessage(data: any): void {
        switch (data.type) {
            case CallID.AD_REWARD:
                this.handleAdReward(data);
                break;
            case CallID.PURCHASE_SUCCESS:
                this.handlePurchaseSuccess(data);
                break;
            default:
                console.log("未处理的iOS消息类型:", data.type);
        }
    }

    /**
     * 处理iOS购买消息
     * @param data 购买数据
     */
    private handleIOSBuyMessage(data: any): void {
        Notifier.send(ListenID.PURCHASE_RESULT, data);
    }

    /**
     * 处理广告奖励
     * @param data 奖励数据
     */
    private handleAdReward(data: any): void {
        Notifier.send(ListenID.AD_REWARD_RECEIVED, data);
    }

    /**
     * 处理购买成功
     * @param data 购买数据
     */
    private handlePurchaseSuccess(data: any): void {
        Notifier.send(ListenID.PURCHASE_SUCCESS, data);
    }

    /**
     * 显示广告
     * @param adType 广告类型
     */
    showAd(adType: string): void {
        if (GameSeting.isIOS) {
            this.callNative("showAd", { type: adType });
        }
    }

    /**
     * 购买商品
     * @param productId 商品ID
     */
    purchase(productId: string): void {
        if (GameSeting.isIOS) {
            this.callNative("purchase", { productId: productId });
        }
    }

    /**
     * 调用原生方法
     * @param method 方法名
     * @param params 参数
     */
    private callNative(method: string, params: any): void {
        // iOS原生调用实现
        console.log(`调用iOS原生方法: ${method}`, params);
    }
}