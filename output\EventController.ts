import { MVC } from "./MVC";
import { Notifier } from "./Notifier";
import { ListenID } from "./ListenID";
import { Manager } from "./Manager";
import { TaSdkID, BMSInfoList } from "./SdkConfig";
import { WonderSdk } from "./WonderSdk";
import EventModel from "./EventModel";

export class EventController extends MVC.MController {
    private _issetLoading: number = 0;
    private thinkingAnalyticsAPI: any = null;

    constructor() {
        super();
        this.setup(EventModel.instance);
        this.changeListener(true);
        this.initEventSdk();
        this._issetLoading = Manager.storage.getNumber("loadingStep", 0);
        this.setSuperProperties({
            ditch_Name: (window as any).wonderSdk.CHANNEL_NAME
        });
    }

    reset(): void {}

    get classname(): string {
        return "EventController";
    }

    registerAllProtocol(): void {}

    changeListener(enable: boolean): void {
        Notifier.changeListener(enable, ListenID.Event_SendEvent, this.onSendEvent, this);
        Notifier.changeListener(enable, ListenID.Event_SetUserProperty, this.setUserProperty, this);
        if (enable) {
            cc.game.on("Event_SetUserProperty", this.setUserProperty, this);
        } else {
            cc.game.off("Event_SetUserProperty", this.setUserProperty, this);
        }
        Notifier.changeListener(enable, ListenID.Event_SetSuperProperties, this.setSuperProperties, this);
        Notifier.changeListener(enable, ListenID.Event_LoginTA, this.loginTa, this);
        Notifier.changeListener(enable, ListenID.Event_LogOutTA, this.logOutTa, this);
        Notifier.changeListener(enable, ListenID.Login_Finish, this.loginFinish, this);
    }

    loginFinish(): void {
        this.setUserProperty("user_setOnce", {
            InitialGameVersion: (window as any).wonderSdk.BMS_VERSION
        });
    }

    onSendEvent(eventName: string, data: any): void {
        if (!("reward_btn" == eventName && "success" != data.Type)) {
            console.log("[SendEvent] " + eventName + "  " + JSON.stringify(data));
            this.thinkingAnalyticsAPI && this.thinkingAnalyticsAPI.track(eventName, data);
        }
    }

    setSuperProperties(properties: any): void {
        this.thinkingAnalyticsAPI && this.thinkingAnalyticsAPI.setSuperProperties(properties);
    }

    setUserProperty(type: string, properties: any): void {
        if (properties) {
            if ("object" == typeof properties) {
                if (this.thinkingAnalyticsAPI) {
                    if ("user_set" == type) {
                        this.thinkingAnalyticsAPI.userSet(properties);
                    } else if ("user_add" == type) {
                        this.thinkingAnalyticsAPI.userAdd(properties);
                    } else if ("user_setOnce" == type) {
                        this.thinkingAnalyticsAPI.userSetOnce(properties);
                    }
                }
            } else {
                cc.error("setProperty is not a object", properties);
            }
        }
    }

    initEventSdk(): void {
        cc.log("taid", TaSdkID[WonderSdk._instance.platformId]);
        if (TaSdkID[WonderSdk._instance.platformId]) {
            const config = {
                appId: TaSdkID[WonderSdk._instance.platformId],
                serverUrl: "",
                enableNative: true,
                autoTrack: {
                    appShow: true,
                    appHide: true,
                    appClick: true,
                    appView: true,
                    appCrash: true,
                    appInstall: true
                },
                enableLog: false
            };
            // this.thinkingAnalyticsAPI = new (window as any).ThinkingAnalyticsAPI(config);
            // this.thinkingAnalyticsAPI.init();
        }
    }

    loginTa(userId: string): void {
        if (this.thinkingAnalyticsAPI) {
            this.thinkingAnalyticsAPI.login(userId);
            this.mode.userVo.userId = userId;
            this.mode.userVo.version = BMSInfoList[(window as any).wonderSdk.platformId].BMS_VERSION;
            console.log("[TaEventController.userVo]", this.mode.userVo);
            this.setUserProperty("user_set", this.mode.userVo);
            this.setSuperProperties(this.mode.userVo);
            Notifier.send(ListenID.Event_SendEvent, "user_Login", {
                ad_id: this.mode.userVo.ad_id,
                userId: userId
            });
            this.setUserProperty("user_set", {
                receiveLiveGift: Manager.storage.getString("record_live_gift_code", "")
            });
        } else {
            console.log("loginTa init fail");
        }
    }

    logOutTa(): void {
        this.thinkingAnalyticsAPI && this.thinkingAnalyticsAPI.logout();
    }

    getPresetProperties(): string | null {
        if (this.thinkingAnalyticsAPI) {
            const properties = this.thinkingAnalyticsAPI.getPresetProperties().toEventPresetProperties();
            properties.client_version = BMSInfoList[(window as any).wonderSdk.platformId].BMS_VERSION;
            return JSON.stringify(properties);
        }
        return null;
    }
}
