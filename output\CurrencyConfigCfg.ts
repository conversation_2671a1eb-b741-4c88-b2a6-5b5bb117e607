import { TConfig } from "./TConfig";

export enum CurrencyConfigDefine {
    Energy = 1,
    Coin = 2,
    Crystal = 3,
    GOLD = 4,
    Vedio = 5,
    Mana = 6,
    Amethyst = 7,
    Amethyst_In = 8,
    Box = 9,
    Equipfragments = 10,
    Diamond = 11,
    BEquipfragments = 12,
    AEquipfragments = 13,
    SEquipfragments = 14,
    silver = 15,
    adcoupons_in = 16,
    adcoupons_out = 17,
    score = 18,
    boxkey = 19,
    advboxkey = 20,
    purchases = 21,
    adcoupons_gift = 22,
    DayTaskCoin = 23,
    WeekTaskCoin = 24,
    High_Box = 25,
    Share = 26,
    buffSelect = 27,
    dragonBall1 = 28,
    dragonBall2 = 29,
    dragonBall3 = 30,
    dragonBall4 = 31,
    dragonBall5 = 32,
    dragonBall6 = 33,
    dragonBall7 = 34,
    buffDrop = 35,
    ranBuffDrop = 36,
    boxBuffDrop = 37,
    sbuffSelect = 38,
    Herofragments_dz = 103,
    Herofragments_ed = 104,
    Herofragments_hzg = 105,
    Herofragments_sb = 106,
    daggerEquipfragments = 1000,
    knucklesEquipfragments = 1001,
    dartsEquipfragments = 1002,
    molotovEquipfragments = 1003,
    bowEquipfragments = 1004,
    icegunEquipfragments = 1005,
    vestEquipfragments = 1006,
    axeEquipfragments = 1007,
    boomerangEquipfragments = 1008,
    sheildEquipfragments = 1009,
    sniperrifleEquipfragments = 1010,
    dynamiteEquipfragments = 1011,
    laserEquipfragments = 1012,
    swordEquipfragments = 1013,
    landminesfragments = 1015,
    Herofragments_xs = 3000,
    Herofragments_xh = 3001,
    Herofragments_xm = 3002,
    fightreward = 4000
}

export class CurrencyConfigCfgReader extends TConfig {
    protected _name: string = "CurrencyConfig";
}
